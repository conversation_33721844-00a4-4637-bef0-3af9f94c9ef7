LinkChecker
===========


Instructions
------------

Clone the repository. Import each folder as a separate project into Eclipse (or youe IDE of choice).

**mayo-linkchecker-core:** contains core linkchecker code and builds the executable run on the server to do the linkchecking.

**mayo-linkchecer** contains the webservice

There is a 
[WordPress Plugin](https://dev.azure.com/MSS-Intranet/_git/charlie?path=%2Fpublic%2Fwp-content%2Fplugins%2Fmayo-common%2Flinkchecker)
that integrates with the LinkChecker webservice.


Deployment
----------

<PERSON><PERSON>he<PERSON> is currently deployed to a Windows Server: ROENEO905A.

Stop the service via the Task Scheduler

The core is copied to D:\linkchecker

The webservice WAR is copied to D:\apache-tomcat-9.0.26\webapps

Restart the task.


Maintenance
-----------

<PERSON><PERSON><PERSON><PERSON> will occasionally crash and need to be killed.

There are password changes twice a year that require an update.

**WARNING:** You need to make sure to restart the task in Task Scheduler and enter the new password.
