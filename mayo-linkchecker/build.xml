<?xml version="1.0" encoding="UTF-8" ?> 
<project name="mayo-linkchecker" default="all" basedir=".">
<property file="${basedir}/build.properties"/>
<path id="class.path">
	<fileset file="${lib.dir}">
		<include name="**/*.jar"/>
	</fileset>
</path>		
<target name="all">
	<echo message="Starting 'mayo-linkchecker.war' build." />
	<antcall target="package.dsc"/>
	<echo message="Ending 'mayo-linkchecker.war' build." />
</target>
<target name="package.dsc" description="mayo-linkchecker.war" depends="prepare">
	<jar destfile="${deploy.dir}/mayo-linkchecker.war">
		<zipfileset dir="${webapp.dir}" includes="**/*.*" excludes="WEB-INF/lib/internal/*.jar,**/test/*.class"/>
		<zipfileset dir="${src.dir}" includes="**/*.xml,**/*.java" excludes="**/test/*.java"/>
	</jar>
</target>
<target name="prepare" depends="clean">
	<mkdir dir="${classes.dir}"/>
	<mkdir dir="${deploy.dir}"/>
	<mkdir dir="${lib.dir}"/>
</target>
<target name="clean" depends="prereq_build">
	<delete dir="${classes.dir}" quiet="true"/>
	<delete dir="${deploy.dir}" quiet="true"/>
</target>
<target name="prereq_build">
	<echo message="Starting 'mayo-linkchecker-core.jar' build." />
	<ant antfile="../mayo-linkchecker-core/build.xml" dir="../mayo-linkchecker-core/" inheritAll="false"/>
	<sync todir="${lib.dir}">
	  <fileset dir="${core.lib.dir}"/>
	</sync>	
	<copy file="../mayo-linkchecker-core/deploy/lib/mayo-linkchecker-core.jar" todir="${lib.dir}" />	
	<echo message="Finished 'mayo-linkchecker-core.jar' build." />
	<echo message="Pre-req build complete." />
</target>
</project>