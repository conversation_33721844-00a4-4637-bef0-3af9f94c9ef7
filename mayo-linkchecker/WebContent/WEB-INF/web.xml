<?xml version="1.0" encoding="UTF-8"?>
<web-app id="mayo-linkchecker" version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<display-name>mayo-linkchecker</display-name>
	<servlet>
	  <servlet-name>LinkCheckerReportServlet</servlet-name> 
	  <servlet-class>edu.mayo.web.linkchecker.servlet.LinkCheckerReportServlet2</servlet-class> 
	</servlet>
	<servlet-mapping>
	  <servlet-name>LinkCheckerReportServlet</servlet-name> 
	  <url-pattern>/report</url-pattern> 
	</servlet-mapping>
	<servlet>
	  <servlet-name>LinkCheckerRequestServlet</servlet-name> 
	  <servlet-class>edu.mayo.web.linkchecker.servlet.LinkCheckerRequestServlet</servlet-class> 
	</servlet>
	<servlet-mapping>
	  <servlet-name>LinkCheckerRequestServlet</servlet-name> 
	  <url-pattern>/request</url-pattern> 
	</servlet-mapping>
	<servlet>
	  <servlet-name>LinkCheckerWarningServlet</servlet-name> 
	  <servlet-class>edu.mayo.web.linkchecker.servlet.LinkCheckerWarningServlet</servlet-class> 
	</servlet>
	<servlet-mapping>
	  <servlet-name>LinkCheckerWarningServlet</servlet-name> 
	  <url-pattern>/warning</url-pattern> 
	</servlet-mapping>
</web-app>
