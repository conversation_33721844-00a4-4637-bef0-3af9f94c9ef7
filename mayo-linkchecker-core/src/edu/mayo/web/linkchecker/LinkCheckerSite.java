package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.io.StringWriter;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.json.JSONObject;
import org.json.XML;

import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerWordPressService;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

@XmlRootElement
public class LinkCheckerSite implements Serializable {

	private static final long serialVersionUID = -8852099693162609206L;

	private URI url;
	private int id = -1;
	private List<LinkCheckerPage> pages = new ArrayList<LinkCheckerPage>();
	private long scanDuration = 0;
	private Date scanDate = new Date();
	private int pageCount = 0;
	private int linkCount = 0;
	private WordpressSite wpSite = new WordpressSite();
	private Hashtable<Integer, String> statusMessages = new Hashtable<Integer, String>();
	private Hashtable<Integer, String> warningMessages = new Hashtable<Integer, String>();
	

	public LinkCheckerSite() {
	}

	public LinkCheckerSite(URI url) throws Exception {
		this.url = LinkCheckerContext.formatSiteUrl(url);
	}
	
	@XmlElement
	public Hashtable<Integer, String> getStatusMessages() {
		return statusMessages;
	}

	public void setStatusMessages(Hashtable<Integer, String> statusMessages) {
		this.statusMessages = statusMessages;
	}
	
	@XmlElement
	public Hashtable<Integer, String> getWarningMessages() {
		return warningMessages;
	}

	public void setWarningMessages(Hashtable<Integer, String> warningMessages) {
		this.warningMessages = warningMessages;
	}

	@XmlTransient
	public WordpressSite getWpSite() {
		return wpSite;
	}

	public void setWpSite(WordpressSite wpSite) {
		this.wpSite = wpSite;
	}

	public void checkSite(LinkCheckerRequest request) throws Exception {
		long startTime = System.currentTimeMillis();
		this.scanDuration = 0;

		try {
			this.clearSiteReport();

			pageCount = 0;
			linkCount = 0;
			// check each page in the page list
			LinkCheckerPage tempPage;
			List<LinkCheckerPage> sitePages = this.getPageListFromSite(!request.isIncludePrivate());
			this.pages = new ArrayList<LinkCheckerPage>();
			for (LinkCheckerPage sitePage : sitePages) {
				try {
					pageCount++;
					tempPage = LinkCheckerPage.checkPage(sitePage, this.getId(), request);
					linkCount += tempPage.getLinks().size();
					this.pages.add(tempPage);
				} catch (Exception report) {
					this.scanDuration = 0;
					String msg = "Page check failed: " + sitePage.getUrl().toString() + ", Exception: " + report;
					if (report.getCause() != null) {
						msg = msg + ", Cause: " + report.getCause().getMessage();
					}
					LinkCheckerContext.LOG.error(msg, report);

					// mark the page as an error, and add to list
					sitePage.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
					this.pages.add(sitePage);
				}
			}

			// update the site link check db entry
			this.scanDuration = (System.currentTimeMillis() - startTime);
			this.scanDate = new Date();
			LinkCheckerDatabaseService.writeLinkCheckSite(this);
			
			LinkCheckerContext.LOG.info("Time to Run Scan: " + (this.scanDuration / 1000) + " seconds.");
		} catch (Exception exp) {
			this.scanDuration = 0;
			String msg = "Site link check failed: " + this.getUrl() + ", Exception: " + exp;
			if (exp.getCause() != null) {
				msg = msg + ", Cause: " + exp.getCause().getMessage();
			}
			LinkCheckerContext.LOG.error(msg, exp);
			throw exp;
		}
	}

	@XmlElement
	public List<LinkCheckerPage> getPages() throws Exception {
		if (this.pages.size() == 0) {
			this.pages = LinkCheckerDatabaseService.getLinkCheckPages(this.getId());
		}
		return pages;
	}

	public void setPages(List<LinkCheckerPage> pages) {
		this.pages = pages;
	}

	public JSONObject toJSON() throws Exception {
		return XML.toJSONObject(this.toXML());
	}

	public String toXML() throws Exception {
		JAXBContext jaxbContext = JAXBContext.newInstance(LinkCheckerSite.class);
		Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

		// output pretty printed
		jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
		jaxbMarshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
		StringWriter output = new StringWriter();
		jaxbMarshaller.marshal(this, output);
		return output.toString();
	}

	public String toHTML() throws Exception {
		int oldSiteLinkCount = 0;
		int totalLinkCount = 0;
		// sort links into bad and warning
		for (LinkCheckerPage page : this.getPages()) {
			for (LinkCheckerLink link : page.getLinks()) {
				if (link.getStatus().getStatusCode() == LinkCheckerContext.STATUS_OLD_SITE)
					oldSiteLinkCount++;
				totalLinkCount++;
			}
		}

		// start creating page content
		StringBuffer html = new StringBuffer("<html>");
		// add the style and code via the header
		html.append(LinkCheckerContext.REPORT_HEADER);

		// start the body section
		html.append("<body>\n");
		html.append("<div class=\"site-header\">\n").append("Link Checker Report: <a target=\"_blank\" href=\"")
				.append(this.getUrl()).append("\">").append(this.getUrl()).append("</a>\n").append("</div>\n");

		// site summary section
		html.append("<div class=\"summary-box\">\n").append("<div class=\"summary\">\n")
				.append("Pages with flagged links: ").append(this.getPages().size()).append("<br>\n")
				.append("Scan duration: ").append((this.getScanDuration() / 1000)).append(" seconds\n").append("<br>\n")
				.append("Scan Date: ").append(this.getScanDate()).append("<br>\n").append("Pages Checked: ")
				.append(this.getPageCount()).append("<br>\n").append("Links Checked: ").append(this.getLinkCount())
				.append("<br>\n").append("Flagged Links: ").append(totalLinkCount).append("<br>\n");
		// add old site link count, if exists
		if (oldSiteLinkCount > 0)
			html.append("Links to old site(s): ").append(oldSiteLinkCount).append("<br>\n");
		html.append("<div class=\"all-buttons\">").append("</div>").append("</div>\n").append("</div><br>\n");

		// page report section

		// create pages div
		for (LinkCheckerPage page : this.getPages()) {
			// craft the 'page-box' sections
			if (page.getLinks().size() > 0) {
				String relativeUrl = page.getUrl().toString().substring(LinkCheckerContext.WP_URL.length());

				html.append("<div class=\"page-box\">\n").append("<div class=\"header\">\n")
						.append("<div class=\"title\">\n").append("<a target=\"_blank\" href=\"").append(page.getUrl())
						.append("\">").append(relativeUrl).append("</a>")
						.append("&nbsp;&nbsp;&nbsp;<span class=\"page-summary\">").append(page.getLinks().size())
						.append(" flagged link(s)</span></div>\n").append("</div>\n");

				html.append("<div class=\"inside\">\n");
				// write the bad link table
				// alternate is for table row striping (and table header, too)
				boolean alternate = true;

				// create the page table
				html.append("<table class=\"link-table");
				html.append("\" border=\"1\">\n").append("<tbody>\n");

				// for each link, add to a table
				for (LinkCheckerLink link : page.getLinks()) {
					html.append("<tr");
					if (alternate) {
						html.append(" class=\"alternate\"");
						alternate = false;
					} else
						alternate = true;

					html.append(">\n");
					String quoteChar = "\"";
					;
					if (link.getHref().toString().contains(quoteChar))
						quoteChar = "'";
					if (link.getTag().equalsIgnoreCase("a") && link.getCaption().trim().length() > 0) {
						html.append("<td><a target=\"_blank\" href=").append(quoteChar).append(link.getHref())
								.append(quoteChar).append(">");
						if (link.getCaption().length() > LinkCheckerContext.LINK_CHECKER_REPORT_MAX_LINK_TEXT_LENGTH) {
							html.append(link.getCaption().substring(0,
									LinkCheckerContext.LINK_CHECKER_REPORT_MAX_LINK_TEXT_LENGTH)).append("...");
						} else
							html.append(link.getCaption());

						html.append("</a></td>\n");
					} else
						html.append("<td><a target=\"_blank\" href=").append(quoteChar).append(link.getHref())
								.append(quoteChar).append(">").append(link.getHref()).append("</a></td>\n");
					html.append("<td>" + link.getStatus().getStatusMessage() + "</td>\n")
							// .append("<td>"+link.getStatus().getHttpStatusCode()+"</td>\n")
							.append("</tr>\n");
				}

				html.append("</tbody></table><br>\n").append("</div>\n").append("</div>\n");
			}
		}
		html.append("</body></html>\n");
		return html.toString();
	}

	@XmlElement
	public long getScanDuration() {
		return scanDuration;
	}

	public void setScanDuration(long scanDuration) {
		this.scanDuration = scanDuration;
	}

	@XmlElement
	public String getFormattedScanDuration() {
		int seconds = (int) (this.scanDuration / 1000) % 60;
		int minutes = (int) ((this.scanDuration / (1000 * 60)) % 60);
		int hours = (int) ((this.scanDuration / (1000 * 60 * 60)) % 24);
		if (hours > 0)
			return hours + " hours, " + minutes + " minutes, " + seconds + " seconds";
		else if (minutes > 0)
			return minutes + " minutes, " + seconds + " seconds";
		else
			return seconds + " seconds";
	}

	@XmlElement
	public Date getScanDate() {
		return scanDate;
	}

	public void setScanDate(Date scanDate) {
		this.scanDate = scanDate;
	}

	@XmlElement
	public String getFormattedScanDate() {
		SimpleDateFormat sdf = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss");
		return sdf.format(this.scanDate);
	}

	@XmlElement
	public int getPageCount() {
		return pageCount;
	}

	public void setPageCount(int pageCount) {
		this.pageCount = pageCount;
	}

	@XmlElement
	public int getLinkCount() {
		return linkCount;
	}

	public void setLinkCount(int linkCount) {
		this.linkCount = linkCount;
	}

	@XmlElement
	public URI getUrl() {
		return url;
	}

	public void setUrl(URI url) {
		this.url = LinkCheckerContext.formatSiteUrl(url);
	}

	@XmlElement
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	private List<LinkCheckerPage> getPageListFromSite(boolean isPublishedOnly) {
		if (pages.size() == 0) {
			try {
				this.pages = LinkCheckerWordPressService.getWordPressPages(this, isPublishedOnly);
			} catch (Exception e) {
				LinkCheckerContext.LOG.error("Error loading pages from site: url: " + this.getUrl(), e);
			}
		}
		return pages;
	}

	public void clearSiteReport() throws Exception {
		LinkCheckerDatabaseService.clearSitePages(this.getId());
		this.linkCount = 0;
		this.pageCount = 0;
		this.scanDate = new Date();
		this.pages = new ArrayList<LinkCheckerPage>();
		this.scanDuration = 0;
		updateDatabase();
	}

	public void updateDatabase() throws Exception {
		LinkCheckerDatabaseService.writeLinkCheckSite(this);
	}

}
