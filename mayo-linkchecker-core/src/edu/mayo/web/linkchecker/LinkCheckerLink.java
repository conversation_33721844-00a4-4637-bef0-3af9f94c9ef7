package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.net.URI;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import edu.mayo.web.linkchecker.handlers.BasicFileLinkHandler;
import edu.mayo.web.linkchecker.handlers.HttpLinkHandler;
import edu.mayo.web.linkchecker.handlers.WarningsLinkHandler;

@XmlRootElement
public class LinkCheckerLink implements Serializable {

	private static final long serialVersionUID = -5012739196788848864L;
	private String tag = "", caption = "";
	private LinkCheckerLinkStatus status = new LinkCheckerLinkStatus();
	private URI target = null;
	private String href = "";

	public LinkCheckerLink() {
	}

	@XmlElement
	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	@XmlTransient
	public URI getTarget() {
		return target;
	}

	public void setTarget(URI target) {
		this.target = target.normalize();
	}

	@XmlTransient
	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		if (tag.length() > LinkCheckerContext.DB_MAX_TAG_LENGTH)
			tag = tag.substring(0, LinkCheckerContext.DB_MAX_TAG_LENGTH);
		this.tag = tag.trim();
	}

	@XmlElement
	public String getCaption() {
		return caption;
	}

	public void setCaption(String caption) {
		if (caption.length() > LinkCheckerContext.DB_MAX_CAPTION_LENGTH)
			caption = caption.substring(0, LinkCheckerContext.DB_MAX_CAPTION_LENGTH);
		this.caption = caption.trim();
	}

	@XmlElement
	public LinkCheckerLinkStatus getStatus() {
		return status;
	}

	public void setStatus(LinkCheckerLinkStatus status) {
		this.status = status;
	}

	public void checkLink(LinkCheckerRequest request) {
		status = new LinkCheckerLinkStatus();
		long startTime = System.currentTimeMillis();
		if (target == null) {
			status.setStatusCode(LinkCheckerContext.STATUS_404);
		} else {
			try {
				LinkCheckerLinkStatus testStatus = LinkCheckerContext.getCachedLinkStatus(target);
				if (testStatus != null) {
					status = testStatus;
				} else {
					// matches one of the passed in conditionals
					for (LinkCheckerConditional conditional : request.getConditionals()) {
						if (conditional.evaluate(target)) {
							status.setStatusCode(LinkCheckerContext.STATUS_OLD_SITE);
							break;
						}
					}

					// if it is not a OLD_SITE url, we iterate through the link
					// handlers to check the link
					if (status.getStatusCode() != LinkCheckerContext.STATUS_OLD_SITE) {
						if (WarningsLinkHandler.matches(target)) {
							status = WarningsLinkHandler.evaluate(target);
							// TODO: enable basic file handling after moving to
							// Windows
						} else if (BasicFileLinkHandler.matches(target)) {
							status = BasicFileLinkHandler.evaluate(target);
						} else if (HttpLinkHandler.matches(target)) {
							status = HttpLinkHandler.evaluate(target);
						}
					}

					LinkCheckerContext.addToCheckedLinksCache(target, status);
				}
			} catch (Exception e) {
				status.setScanTime((System.currentTimeMillis() - startTime));
				LinkCheckerContext.LOG.debug("Unknown Error checking link: " + getTarget(), e);
				status.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
				LinkCheckerContext.addToCheckedLinksCache(getTarget(), status);
			}
		}
	}

}
