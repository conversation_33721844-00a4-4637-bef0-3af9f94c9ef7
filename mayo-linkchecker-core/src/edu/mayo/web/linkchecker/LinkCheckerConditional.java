package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

public class LinkCheckerConditional implements Serializable {
	private static final long serialVersionUID = 1L;
	private URI pattern = null;
	private int statusCode = LinkCheckerContext.STATUS_OK;
	private List<URI> exceptions = new ArrayList<URI>();
	private String patternLowerCase = "";

	public LinkCheckerConditional(URI pattern, int statusCode) {
		this.pattern = LinkCheckerContext.formatLinkUrl(pattern);
		this.patternLowerCase = pattern.toString().toLowerCase();
		this.statusCode = statusCode;
	}

	public boolean evaluate(URI link) {
		boolean result = false;
		// check to see if we have a match
		if (link.toString().toLowerCase().startsWith(patternLowerCase))
			result = true;

		// if we have a match, check to make sure that it isn't an exception
		if (result == true) {
			for (URI exception : this.exceptions) {
				if (link.toString().toLowerCase().startsWith(exception.toString().toLowerCase())) {
					result = false;
					break;
				}
			}
		}

		return result;
	}

	public URI getPattern() {
		return pattern;
	}

	public void setPattern(URI pattern) {
		this.pattern = pattern;
		this.patternLowerCase = this.pattern.toString().toLowerCase();
	}

	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	public List<URI> getExceptions() {
		return exceptions;
	}

	public void setExceptions(List<URI> exceptions) {
		this.exceptions = exceptions;
	}

}
