package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.io.StringWriter;
import java.net.URI;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.json.JSONObject;
import org.json.XML;

@XmlRootElement
public class Link<PERSON>heckerWarning implements Serializable {
	private static final long serialVersionUID = 6838808544114300024L;
	private int id = -1;
	private URI pattern = null;
	private boolean isExactMatch = false;
	private String message = "";
	private boolean isActive = false;

	public LinkCheckerWarning() {
	}

	public LinkCheckerWarning(int id, URI pattern, boolean isExactMatch, String message, boolean isActive) {
		this.id = id;
		this.pattern = LinkCheckerContext.formatLinkUrl(pattern);
		this.isExactMatch = isExactMatch;
		this.message = message;
		this.isActive = isActive;
	}

	public boolean evaluate(URI link) {
		boolean result = false;
		// check to see if we have a match
		if (this.isExactMatch) {
			if (link.toString().equalsIgnoreCase(pattern.toString()))
				result = true;
		} else {
			if (link.toString().toLowerCase().startsWith(pattern.toString().toLowerCase()))
				result = true;
		}

		return result;
	}

	@XmlElement
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@XmlElement
	public URI getPattern() {
		return pattern;
	}

	public void setPattern(URI pattern) {
		this.pattern = LinkCheckerContext.formatLinkUrl(pattern);
	}

	@XmlElement
	public boolean isExactMatch() {
		return isExactMatch;
	}

	public void setExactMatch(boolean isExactMatch) {
		this.isExactMatch = isExactMatch;
	}

	@XmlElement
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	@XmlElement
	public boolean isActive() {
		return isActive;
	}

	public void setActive(boolean isActive) {
		this.isActive = isActive;
	}

	public JSONObject toJSON() throws Exception {
		return XML.toJSONObject(this.toXML());
	}

	public String toXML() throws Exception {
		JAXBContext jaxbContext = JAXBContext.newInstance(LinkCheckerWarning.class);
		Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

		// output pretty printed
		jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
		jaxbMarshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
		StringWriter output = new StringWriter();
		jaxbMarshaller.marshal(this, output);
		return output.toString();
	}

}
