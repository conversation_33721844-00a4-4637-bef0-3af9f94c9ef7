package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.jsoup.select.Selector;

import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.wordpress.WordpressPost;

@XmlRootElement
public class LinkCheckerPage implements Serializable {

	private static final long serialVersionUID = 3574377274977066587L;

	private URI url = null;
	private List<LinkCheckerLink> links = new ArrayList<LinkCheckerLink>();
	private long id = -1;
	private int statusCode = 0;
	private long siteId = -1;
	private boolean isPost = false;
	private boolean isPrivate = false;
	private WordpressPost wpPost = new WordpressPost();

	public LinkCheckerPage(URI url) {
		this.url = url;
	}

	public LinkCheckerPage() {
	}
	
	@XmlTransient
	public WordpressPost getWpPost() {
		return wpPost;
	}

	public void setWpPost(WordpressPost wpPost) {
		this.wpPost = wpPost;
	}

	@XmlElement
	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	@XmlElement
	public URI getUrl() {
		return url;
	}

	public void setUrl(URI url) {
		this.url = url;
	}

	@XmlElement
	public List<LinkCheckerLink> getLinks() {
		return links;
	}

	public void setLinks(List<LinkCheckerLink> links) {
		this.links = links;
	}

	@XmlTransient
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	@XmlTransient
	public boolean isFlagged() {
		boolean flagged = false;

		for (LinkCheckerLink link : getLinks()) {
			if (link.getStatus().isFlagged()) {
				flagged = true;
				break;
			}
		}

		return flagged;
	}

	@XmlTransient
	public long getSiteId() {
		return siteId;
	}

	public void setSiteId(long siteId) {
		this.siteId = siteId;
	}

	@XmlTransient
	public boolean isPost() {
		return isPost;
	}

	public void setPost(boolean isPost) {
		this.isPost = isPost;
	}

	@XmlTransient
	public boolean isPrivate() {
		return isPrivate;
	}

	public void setPrivate(boolean isPrivate) {
		this.isPrivate = isPrivate;
	}

	public static LinkCheckerPage checkPage(LinkCheckerPage page, int siteId, LinkCheckerRequest request)
			throws Exception {
		page.setLinks(LinkCheckerPage.getLinksFromPage(page));
		LinkCheckerContext.LOG.debug("checking Page: " + page.getUrl() + " Links: " + page.getLinks().size());

		if (page.getLinks() == null || page.getLinks().size() < 1) {
			return page;
		} else {
			for (LinkCheckerLink link : page.getLinks()) {
				link.checkLink(request);
			}
		}

		// write the page and links to the DB
		LinkCheckerDatabaseService.writeLinkCheckPage(page, siteId);

		// return the count of links checked
		return page;
	}
	
	//parse content and find the included links
	public static List<LinkCheckerLink> getLinksFromPage(LinkCheckerPage page) throws Exception {
		List<LinkCheckerLink> links = new ArrayList<LinkCheckerLink>();
		
		Document doc = Jsoup.parse(page.getWpPost().getPostContent());

		// get the links from the postContent
		Elements allLinks = Selector.select(LinkCheckerContext.LINK_CHECKER_LINK_SELECTOR, doc);

		// iterate through the links and normalize/standardize the URL
		// we do a lot of 'fixing' URLs to match IE's loose
		// standards--especially on 'file://' links
		LinkCheckerLink link;
		for (Element pageElement : allLinks) {
			// create the LinkCheckLink object to add to the PageLink object
			link = new LinkCheckerLink();
			link.setTag(pageElement.tag().getName());

			String linkTarget = pageElement.attr("abs:href");
			if (linkTarget == null || linkTarget.length() == 0) {
				// if we have no text in the link, look for an image
				Elements images = pageElement.select("img");
				if (images.size() > 0)
					linkTarget = "LINKED IMAGE[\'" + images.first().attr("src") + "\']";
				else
					linkTarget = pageElement.attr("abs:src");
			}
			link.setHref(linkTarget);

			String linkTargetLower = linkTarget.toLowerCase();
			if (linkTarget.contains("/\\\\"))
				linkTarget = "file://" + linkTarget.substring(linkTarget.indexOf("/\\\\") + 3).replace("\\", "/");
			else if (linkTargetLower.startsWith(LinkCheckerContext.LINK_CHECKER_MFAD_STRING.toLowerCase()))
				linkTarget = "file://" + linkTarget;
			else if (linkTargetLower.startsWith("\\\\" + LinkCheckerContext.LINK_CHECKER_MFAD_STRING.toLowerCase())) {
				int index = linkTarget.indexOf("\\\\" + LinkCheckerContext.LINK_CHECKER_MFAD_STRING.toLowerCase());
				linkTarget = "file:" + linkTarget.substring(index).replace('\\', '/');
			}
			linkTargetLower = linkTarget.toLowerCase();

			if (linkTargetLower.startsWith("file:")) {
				// normalize the filename from the file URL - these are all
				// valid file URL patterns for IE
				if (linkTarget.startsWith("file://///"))
					linkTarget = "file://" + linkTarget.substring(10);
				else if (linkTarget.startsWith("file:////"))
					linkTarget = "file://" + linkTarget.substring(9);
				else if (linkTarget.startsWith("file:///"))
					linkTarget = "file://" + linkTarget.substring(8);
				else if (linkTarget.startsWith("file://"))
					linkTarget = "file://" + linkTarget.substring(7);
				else if (linkTarget.startsWith("file:/"))
					linkTarget = "file://" + linkTarget.substring(6);
				else
					linkTarget = "file://" + linkTarget.substring(5);

				linkTargetLower = linkTarget.toLowerCase();
			}

			// ignore WordPress admin pages, add only 'http', 'https' and 'file'
			// links
			if (linkTargetLower.indexOf(LinkCheckerContext.WP_ADMIN_URL) < 0
					&& linkTargetLower.indexOf(LinkCheckerContext.WP_LOGIN_URL) < 0
					&& (linkTargetLower.startsWith("file:") || linkTargetLower.startsWith("http:")
							|| linkTargetLower.startsWith("https:"))) {
				try {
					// clean up the linkTarget
					// TODO: how to handle links that can't even be turned into
					// URLs?
					linkTarget = linkTarget.replace("\\", "/").replace(" ", "%20").replace("[", "%5B").replace("]",
							"%5D");
					String caption = pageElement.hasText() ? pageElement.text() : pageElement.attr("alt");
					link.setCaption(caption);
					link.setTag(pageElement.tag().toString());
					link.setTarget(LinkCheckerContext.formatLinkUrl(new URI(linkTarget)));
					links.add(link);
				} catch (Exception report) {
					LinkCheckerContext.LOG.error("Error creating link from <a> tag: " + report.getMessage());
				}
			}
		}

		return links;
	}

}
