package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.io.StringWriter;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.json.JSONObject;
import org.json.XML;

@XmlRootElement
public class LinkCheckerRequest implements Serializable {

	private static final long serialVersionUID = 1145316802380938125L;
	private boolean includePrivate = false;
	private boolean includePosts = false;
	private ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
	private ArrayList<URI> conditionalExcludes = new ArrayList<URI>();
	private URI siteUrl = null;
	private String emailAddress = "";
	private int priority = LinkCheckerContext.PRIORITY_LOW;
	private int id = 0;
	private int statusCode = LinkCheckerContext.STATUS_NOT_STARTED;
	private Date insertDate = new Date();
	private String errorMessage = "";

	public LinkCheckerRequest() {
	}

	@XmlElement
	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	@XmlElement
	public ArrayList<LinkCheckerConditional> getConditionals() {
		return conditionals;
	}

	public void setConditionals(ArrayList<LinkCheckerConditional> conditionals) {
		this.conditionals = conditionals;
	}

	@XmlElement
	public boolean isIncludePrivate() {
		return includePrivate;
	}

	public void setIncludePrivate(boolean includePrivate) {
		this.includePrivate = includePrivate;
	}

	@XmlElement
	public boolean isIncludePosts() {
		return includePosts;
	}

	public void setIncludePosts(boolean includePosts) {
		this.includePosts = includePosts;
	}

	@XmlElement
	public URI getSiteUrl() {
		return this.siteUrl;
	}

	public void setSiteUrl(URI siteUrl) {
		this.siteUrl = LinkCheckerContext.formatSiteUrl(siteUrl);
	}

	@XmlElement
	public int getPriority() {
		return priority;
	}

	public void setPriority(int priority) {
		this.priority = priority;
	}

	@XmlElement
	public ArrayList<URI> getConditionalExcludes() {
		return conditionalExcludes;
	}

	public void setConditionalExcludes(ArrayList<URI> conditionalExcludes) {
		this.conditionalExcludes = conditionalExcludes;
	}

	@XmlElement
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@XmlElement
	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	@XmlElement
	public Date getInsertDate() {
		return insertDate;
	}

	public void setInsertDate(Date insertDate) {
		this.insertDate = insertDate;
	}

	@XmlElement
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public JSONObject toJSON() throws Exception {
		return XML.toJSONObject(this.toXML());
	}

	public String toXML() throws Exception {
		JAXBContext jaxbContext = JAXBContext.newInstance(LinkCheckerRequest.class);
		Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

		// output pretty printed
		jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
		jaxbMarshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
		StringWriter output = new StringWriter();
		jaxbMarshaller.marshal(this, output);
		return output.toString();
	}

}
