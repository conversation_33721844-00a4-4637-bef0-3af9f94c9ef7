package edu.mayo.web.linkchecker.service;

import java.io.File;
import java.io.OutputStream;
import java.util.List;

import org.apache.commons.io.FileUtils;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.wordpress.WordpressDatabaseUtils;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

public class LinkCheckerService {

	// This function processes an individual link check request
	public static void processRequest(int requestId) throws Exception {
		// get request from the DB
		LinkCheckerRequest request = LinkCheckerDatabaseService.getRequest(requestId);
		// mark the request as STARTED
		request.setStatusCode(LinkCheckerContext.STATUS_STARTED);
		LinkCheckerDatabaseService.writeRequest(request);

		// call LinkCheckerService to execute the request
		LinkCheckerContext.LOG.info("Starting request " + requestId);
		
		LinkCheckerContext.LOG.info("URL: " + request.getSiteUrl());

		// clear the link checker cache
		LinkCheckerContext.clearCheckedLinksCache();

		LinkCheckerSite lcSite = LinkCheckerDatabaseService.getLinkCheckSite(request.getSiteUrl());

		// if site entry exists use it...else create it
		if (lcSite == null) {
			lcSite = new LinkCheckerSite(request.getSiteUrl());
			lcSite.setId(LinkCheckerDatabaseService.writeLinkCheckSite(lcSite));
		}
		
		WordpressSite wpSite = WordpressDatabaseUtils.getWordpressSite(request.getSiteUrl().toString());
		lcSite.setWpSite(wpSite);

		lcSite.checkSite(request);
		
		// mark the request as CLOSED
		request.setStatusCode(LinkCheckerContext.STATUS_CLOSED);
		LinkCheckerDatabaseService.writeRequest(request);

		// TODO: log completion of reporting writing with destination
		LinkCheckerContext.LOG.debug("Writing request " + requestId);

		// load the new site from the DB
		LinkCheckerSite newLcSite = LinkCheckerDatabaseService.getLinkCheckSite(request.getSiteUrl());
		newLcSite.setStatusMessages(LinkCheckerContext.STATUS_MESSAGES);
		newLcSite.setWarningMessages(LinkCheckerDatabaseService.getLinkCheckWarningMessages(false));
		
		//cache it in the reports table for quick access
		LinkCheckerService.writeLinkCheckReport(newLcSite);
		LinkCheckerDatabaseService.writeLinkCheckReport(newLcSite);
		
		// send the email, if included
		if (request.getEmailAddress().trim().length() > 0) {
			try {
				String reportHTML = newLcSite.toHTML();
				LinkCheckerContext.sendEmail(request.getEmailAddress(),
						"Website Link Check Report For: " + newLcSite.getUrl(), reportHTML);
			} catch (Exception e) {
				LinkCheckerContext.LOG.error("Error sending report email: ", e);
			}
		}

		LinkCheckerContext.LOG.info("Finished request " + requestId);
	}

	public static void runNextRequest() throws Exception {
		LinkCheckerRequest runningRequest = LinkCheckerDatabaseService.getRunningRequest();
		// reset any running requests to 'not run' -- assume they are hung
		// requests
		if (runningRequest != null) {
			LinkCheckerContext.LOG
					.info("Request " + runningRequest.getId() + " is running, setting state to 'not run'.");
			runningRequest.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
			LinkCheckerDatabaseService.writeRequest(runningRequest);
		}

		LinkCheckerRequest request = LinkCheckerDatabaseService.getNextRequest();
		//if no next request, reload all wordpress sites in the queue and get the first in the queue
		if (request == null) {
			LinkCheckerContext.LOG.info("No queued requests. Reseting Linkchecker queue.");
			resetQueue();
			request = LinkCheckerDatabaseService.getNextRequest();			
		} 
		
		try {
			LinkCheckerService.processRequest(request.getId());
		} catch (Exception e) {
			// if we run into any problems, reset the current scan as
			// 'ERROR'
			request.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
			request.setErrorMessage(e.getMessage());
			LinkCheckerDatabaseService.writeRequest(request);
			LinkCheckerContext.LOG.error("Error processing request: " + request.getId(), e);
			try {
				String content = "Error processing Link Checker Report for:<br>" + request.getSiteUrl() + "<br><br>"
						+ e.getLocalizedMessage() + "<br>";
				for (StackTraceElement s : e.getStackTrace())
					content += s.toString() + "<br>";
				LinkCheckerContext.sendEmail(LinkCheckerContext.SMTP_ERROR_TO_ADDRESS,
						"ERROR: LinkChecker request failed for: " + request.getSiteUrl(), content);
			} catch (Exception report) {
				LinkCheckerContext.LOG.error("Error sending report email: ", report);
			}
		}
	}

	// function for auto-loading all WP sites for checking
	private static void resetQueue() throws Exception {
		long startTime = System.nanoTime();
//		List<String> siteUrls = LinkCheckerWordPressService.getAllSites(publicOnly);
		List<WordpressSite> sites = LinkCheckerWordPressService.getAllSites();
		
		LinkCheckerDatabaseService.queueAllWordpressSites(sites);
		
		long endTime = System.nanoTime();
		long duration = (endTime - startTime) / 1000000000;
		LinkCheckerContext.LOG.info("WordPress sites loaded in: " + duration + " seconds.");
	}

	public static String getLinkCheckReport(String siteUrl) throws Exception {
		String shortNameString = siteUrl.toString().substring(LinkCheckerContext.WP_URL.length()+1);
		shortNameString = shortNameString.substring(0,shortNameString.length()-1);
		String filename = shortNameString.toLowerCase()+".json";
		
		String report = "";
		try {
			report = FileUtils.readFileToString(new File("reports",filename));
		} catch (Exception e) {
			report = LinkCheckerDatabaseService.getLinkCheckReport(siteUrl);
		}
		return report;
	}
	
	public static void writeLinkCheckReport(LinkCheckerSite site) throws Exception {
		
		String shortNameString = site.getUrl().toString().substring(LinkCheckerContext.WP_URL.length()+1);
		shortNameString = shortNameString.substring(0,shortNameString.length()-1);
		String filename = shortNameString.toLowerCase()+".json";
		String report = site.toJSON().toString();
		
		FileUtils.writeStringToFile(new File(LinkCheckerContext.LINK_CHECKER_REPORT_DIRECTORY,filename), report);
	}
	
	public static OutputStream getLinkCheckReportStream(String siteUrl) throws Exception {
		String shortNameString = siteUrl.toString().substring(LinkCheckerContext.WP_URL.length()+1);
		shortNameString = shortNameString.substring(0,shortNameString.length()-1);
		String filename = shortNameString.toLowerCase()+".json";
		
		return FileUtils.openOutputStream(new File(LinkCheckerContext.LINK_CHECKER_REPORT_DIRECTORY,filename));
	}
	
	
	/**
	 * 
	 * This is the main method for command-line launching of the
	 * LinkCheckerService This method will check the link checker queue DB for
	 * queue-up requests, and execute those requests if present.
	 * 
	 * @param args
	 */
	public static void main(String[] args) {
		try {
			// allow for passing in a local key store file path
			if (args[0] != null)
				LinkCheckerContext.LINK_CHECKER_KEY_STORE_PATH = args[0];
			LinkCheckerService.runNextRequest();
		} catch (Exception e) {
			LinkCheckerContext.LOG.error("Error running LinkCheckerService: ", e);

		}
	}

}
