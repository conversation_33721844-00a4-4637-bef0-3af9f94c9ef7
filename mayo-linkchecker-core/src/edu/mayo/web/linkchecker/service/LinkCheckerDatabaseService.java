package edu.mayo.web.linkchecker.service;

import java.io.File;
import java.net.URI;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

import org.apache.commons.io.FileUtils;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerLink;
import edu.mayo.web.linkchecker.LinkCheckerLinkStatus;
import edu.mayo.web.linkchecker.LinkCheckerPage;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.LinkCheckerWarning;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

public class LinkCheckerDatabaseService {

	private static Connection getConnection() throws Exception {
		Class.forName(LinkCheckerContext.DB_DRIVER);
		return DriverManager.getConnection(LinkCheckerContext.DB_CONNECTION_STRING, LinkCheckerContext.DB_PROPS);
		// return DriverManager.getConnection(LinkCheckerContext.DB_CONNECTION_STRING);
	}

	public static LinkCheckerSite getLinkCheckSite(URI url) throws Exception {
		String selectSql = "SELECT id, scan_duration, scan_date, page_count, link_count FROM LINK_CHECK_SITES where LOWER(url)=?";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			url = LinkCheckerContext.formatSiteUrl(url);
			pStmt = con.prepareStatement(selectSql);
			pStmt.setString(1, url.toString().toLowerCase());
			rs = pStmt.executeQuery();

			if (rs.next()) {
				LinkCheckerSite site = new LinkCheckerSite(url);
				site.setId(rs.getInt(1));
				site.setScanDuration(rs.getLong(2));
				site.setScanDate(new Date(rs.getLong(3)));
				site.setPageCount(rs.getInt(4));
				site.setLinkCount(rs.getInt(5));
				site.setPages(getLinkCheckPages(site.getId(), con));
				for (LinkCheckerPage page : site.getPages()) {
					page.setLinks(getLinkCheckLinks(page.getId()));
				}
				return site;
			}
			return null;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check site: siteUrl=" + url, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerSite getLinkCheckSite(int siteId) throws Exception {
		String selectSql = "SELECT url, scan_duration, scan_date, page_count, link_count FROM LINK_CHECK_SITES where id=?";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			pStmt = con.prepareStatement(selectSql);
			pStmt.setInt(1, siteId);
			rs = pStmt.executeQuery();

			if (rs.next()) {
				LinkCheckerSite site = new LinkCheckerSite(new URI(rs.getString(1)));
				site.setScanDuration(rs.getLong(2));
				site.setScanDate(new Date(rs.getLong(3)));
				site.setPageCount(rs.getInt(4));
				site.setLinkCount(rs.getInt(5));
				site.setPages(getLinkCheckPages(siteId, con));
				for (LinkCheckerPage page : site.getPages()) {
					page.setLinks(getLinkCheckLinks(page.getId()));
				}
				site.setId(siteId);
				return site;
			}
			return null;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check site: siteId=" + siteId, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static List<LinkCheckerPage> getLinkCheckPages(int siteId) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getLinkCheckPages(siteId, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static List<LinkCheckerPage> getLinkCheckPages(int siteId, Connection con) throws Exception {
		String sql = "SELECT id, url, status_code, is_post, is_private FROM LINK_CHECK_PAGES WHERE site_id=? ";
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		List<LinkCheckerPage> pages = new ArrayList<LinkCheckerPage>();
		try {
			pStmt = con.prepareStatement(sql);
			pStmt.setInt(1, siteId);
			rs = pStmt.executeQuery();

			while (rs.next()) {
				LinkCheckerPage page = new LinkCheckerPage(new URI(rs.getString(2)));
				page.setSiteId(siteId);
				page.setId(rs.getInt(1));
				page.setStatusCode(rs.getInt(3));
				page.setPost(rs.getBoolean(4));
				page.setPrivate(rs.getBoolean(5));
				page.setLinks((LinkCheckerDatabaseService.getLinkCheckLinks(page.getId(), con)));

				pages.add(page);
			}

			return pages;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check pages: siteId=" + siteId, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerPage getLinkCheckPage(int id) throws Exception {
		String sql = "SELECT site_id, url, status_code, is_post, is_private FROM LINK_CHECK_PAGES WHERE id=? ";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			pStmt = con.prepareStatement(sql);
			pStmt.setInt(1, id);
			rs = pStmt.executeQuery();

			if (rs.next()) {
				LinkCheckerPage page = new LinkCheckerPage(new URI(rs.getString(2)));
				page.setId(id);
				page.setSiteId(rs.getInt(1));
				page.setStatusCode(rs.getInt(3));
				page.setPost(rs.getBoolean(4));
				page.setPrivate(rs.getBoolean(5));
				page.setLinks((LinkCheckerDatabaseService.getLinkCheckLinks(page.getId(), con)));
				return page;
			}
			return null;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check page: id=" + id, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerPage getLinkCheckPage(String url) throws Exception {
		String sql = "SELECT id, site_id, status_code, is_post, is_private FROM LINK_CHECK_PAGES WHERE url=? ";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			pStmt = con.prepareStatement(sql);
			pStmt.setString(1, url);
			rs = pStmt.executeQuery();

			if (rs.next()) {
				LinkCheckerPage page = new LinkCheckerPage(new URI(url));
				page.setId(rs.getLong(1));
				page.setSiteId(rs.getLong(2));
				page.setStatusCode(rs.getInt(3));
				page.setPost(rs.getBoolean(4));
				page.setPrivate(rs.getBoolean(5));
				page.setLinks((LinkCheckerDatabaseService.getLinkCheckLinks(page.getId(), con)));
				return page;
			}
			return null;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check page: url=" + url, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static List<LinkCheckerLink> getLinkCheckLinks(long pageId) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getLinkCheckLinks(pageId, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static List<LinkCheckerLink> getLinkCheckLinks(long pageId, Connection con) throws Exception {
		String sql = "SELECT target, tag, caption, status_code, warning_id, href FROM LINK_CHECK_LINKS WHERE page_id=? ";
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		List<LinkCheckerLink> links = new ArrayList<LinkCheckerLink>();
		try {
			pStmt = con.prepareStatement(sql);
			pStmt.setLong(1, pageId);
			rs = pStmt.executeQuery();

			LinkCheckerLink link;
			while (rs.next()) {
				link = new LinkCheckerLink();
				link.setTarget(new URI(rs.getString(1)));
				link.setTag(rs.getString(2));
				link.setCaption(rs.getString(3));
				LinkCheckerLinkStatus status = new LinkCheckerLinkStatus();
				status.setStatusCode(rs.getInt(4));
				status.setWarningId(rs.getInt(5));
				link.setStatus(status);
				link.setHref(rs.getString(6));
				links.add(link);
			}

			return links;
		} catch (Exception e) {
			throw new Exception("Error retrieving page link check links: pageId=" + pageId, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static void clearSitePages(int siteId) throws Exception {
		String deleteLinksSql = "DELETE LINK_CHECK_LINKS FROM LINK_CHECK_LINKS l INNER JOIN LINK_CHECK_PAGES p on l.page_id = p.id WHERE p.site_id = ?";
		String deletePagesSql = "DELETE FROM LINK_CHECK_PAGES WHERE site_id=? ";
		PreparedStatement pStmt = null;
		Connection con = null;

		try {
			con = LinkCheckerDatabaseService.getConnection();
			// clear old entries

			pStmt = con.prepareStatement(deleteLinksSql);
			pStmt.setInt(1, siteId);
			pStmt.execute();

			pStmt = con.prepareStatement(deletePagesSql);
			pStmt.setInt(1, siteId);
			pStmt.execute();

		} catch (Exception e) {
			throw new Exception("Error deleting link check site: siteId=" + siteId, e);
		} finally {
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	
	public static int writeLinkCheckSite(LinkCheckerSite site) throws Exception {
		String updateSql = "UPDATE LINK_CHECK_SITES SET url=?, scan_duration=?, scan_date=?, page_count=?, link_count=? WHERE id=? ";
		String insertSql = "INSERT INTO LINK_CHECK_SITES (url, scan_duration, scan_date, page_count, link_count) VALUES (?,?,?,?,?)";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			LinkCheckerSite tempSite = getLinkCheckSite(site.getId());
			if (tempSite != null) {
				pStmt = con.prepareStatement(updateSql);
				pStmt.setString(1, LinkCheckerContext.formatSiteUrl(site.getUrl()).toString());
				pStmt.setLong(2, site.getScanDuration());
				pStmt.setLong(3, site.getScanDate().getTime());
				pStmt.setInt(4, site.getPageCount());
				pStmt.setInt(5, site.getLinkCount());
				pStmt.setInt(6, site.getId());
				pStmt.executeUpdate();

				return site.getId();
			} else {
				pStmt = con.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);

				pStmt.setString(1, LinkCheckerContext.formatSiteUrl(site.getUrl()).toString());
				pStmt.setLong(2, site.getScanDuration());
				pStmt.setLong(3, site.getScanDate().getTime());
				pStmt.setInt(4, site.getPageCount());
				pStmt.setInt(5, site.getLinkCount());
				pStmt.executeUpdate();

				rs = pStmt.getGeneratedKeys();
				rs.next();
				return rs.getInt(1);
			}
		} catch (Exception e) {
			throw new Exception("Error writing link check site. siteUrl=" + site.getUrl());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static void writeLinkCheckPage_Helper(LinkCheckerPage page, int siteId, Connection con) throws Exception {
		String insertSql = "INSERT INTO LINK_CHECK_PAGES (site_id, url, status_code, is_post, is_private) VALUES (?,?,?,?,?)";

		PreparedStatement insertStmt = null;
		ResultSet rs = null;
		try {
			insertStmt = con.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);

			if (page.isFlagged()) {
				insertStmt.setInt(1, siteId);
				insertStmt.setString(2, page.getUrl().toString());
				insertStmt.setInt(3, page.getStatusCode());
				insertStmt.setBoolean(4, page.isPost());
				insertStmt.setBoolean(5, page.isPrivate());
				insertStmt.executeUpdate();

				rs = insertStmt.getGeneratedKeys();
				if (rs.next()) {
					page.setId(rs.getInt(1));
				}
			}
		} catch (Exception e) {
			throw new Exception("Error writing link check pages: siteId=" + siteId, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}

			if (insertStmt != null) {
				try {
					insertStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static void writeLinkCheckLinks_Helper(LinkCheckerPage page, Connection con) throws Exception {
		String insertSql = "INSERT INTO LINK_CHECK_LINKS (target, tag, caption, status_code, "
				+ "page_id, warning_id, href) VALUES (?,?,?,?,?,?,?)";

		PreparedStatement insertStmt = null;
		try {
			insertStmt = con.prepareStatement(insertSql);
			for (LinkCheckerLink link : page.getLinks()) {
				if (link.getStatus().isFlagged()) {
					insertStmt.setString(1, link.getTarget().toString());
					insertStmt.setString(2, link.getTag());
					insertStmt.setString(3, link.getCaption());
					insertStmt.setInt(4, link.getStatus().getStatusCode());
					insertStmt.setLong(5, page.getId());
					insertStmt.setInt(6, link.getStatus().getWarningId());
					insertStmt.setString(7, link.getHref());
					insertStmt.executeUpdate();
				}
			}
		} catch (Exception e) {
			throw new Exception("Error updating page link check report: ", e);
		} finally {
			if (insertStmt != null) {
				try {
					insertStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static void writeLinkCheckPage(LinkCheckerPage page, int siteId) throws Exception {
		// write page results to the DB
		java.sql.Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			LinkCheckerDatabaseService.writeLinkCheckPage_Helper(page, siteId, con);
			LinkCheckerDatabaseService.writeLinkCheckLinks_Helper(page, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error("Error closing database connection.", report);
				}
			}
		}

	}

	public static LinkCheckerWarning getLinkCheckWarning(int id) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			
			return getLinkCheckWarning(con, id);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static LinkCheckerWarning getLinkCheckWarning(Connection con, int id) throws Exception {
		LinkCheckerWarning warning = null;

		String selectSql = "SELECT pattern, is_exact_match, message, is_active FROM LINK_CHECK_WARNINGS WHERE id=?";
		ResultSet rs = null;
		PreparedStatement pStmt = null;
		try {
			pStmt = con.prepareStatement(selectSql);
			pStmt.setInt(1, id);
			rs = pStmt.executeQuery();

			if (rs.next()) {
				warning = new LinkCheckerWarning(id, new URI(rs.getString(1)), rs.getBoolean(2), rs.getString(3),
						rs.getBoolean(4));
			}
		} catch (Exception e) {
			throw new Exception("Error retrieving link check warning. id=" + id, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}

			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
		return warning;

	}
	
	public static String getLinkCheckReport(String siteUrl) throws Exception {
		String selectSql = "SELECT report FROM LINK_CHECK_REPORTS where site_url=?";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();			
			pStmt = con.prepareStatement(selectSql);
			pStmt.setString(1, siteUrl.toLowerCase());
			rs = pStmt.executeQuery();

			if (rs.next()) {
				Blob blob = rs.getBlob(1);
				if (blob != null) {
					byte[] bdata = blob.getBytes(1, (int)blob.length());
					blob.free();
					return new String(bdata);
				} else
					return "";
			} else
				return "";
		} catch (Exception e) {
			throw new Exception("Error retrieving link check report: siteUrl=" + siteUrl, e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}
	
	public static void writeLinkCheckReport(LinkCheckerSite site) throws Exception {
		String updateSql = "UPDATE LINK_CHECK_REPORTS SET report=? WHERE site_url=? ";
		String insertSql = "INSERT INTO LINK_CHECK_REPORTS (site_url, report) VALUES (?,?)";
		Connection con = null;
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			String report = site.toJSON().toString();
			String existingReport = getLinkCheckReport(site.getUrl().toString().toLowerCase());
			if (existingReport.length() > 0) {
				pStmt = con.prepareStatement(updateSql);
				Blob blob = con.createBlob();
				blob.setBytes(1, report.getBytes());
				pStmt.setBlob(1, blob);
				pStmt.setString(2, site.getUrl().toString().toLowerCase());
				pStmt.executeUpdate();
				blob.free();
			} else {
				pStmt = con.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
				pStmt.setString(1, site.getUrl().toString().toLowerCase());
				Blob blob = con.createBlob();
				blob.setBytes(1, report.getBytes());
				pStmt.setBlob(2, blob);
				pStmt.executeUpdate();
				blob.free();
			}
		} catch (Exception e) {
			throw new Exception("Error writing link check report. siteUrl=" + site.getUrl());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}
	
	

	public static Hashtable<Integer, String> getLinkCheckWarningMessages(boolean includeInactive) throws Exception {
		Hashtable<Integer, String> warningMessages = new Hashtable<Integer, String>();
		List<LinkCheckerWarning> warnings = getLinkCheckWarnings(includeInactive);
		for (LinkCheckerWarning warning : warnings) {
			warningMessages.put(warning.getId(), warning.getMessage());
		}
		
		return warningMessages;
	}
	
	
	public static List<LinkCheckerWarning> getLinkCheckWarnings(boolean includeInactive) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			
			return getLinkCheckWarnings(includeInactive, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static List<LinkCheckerWarning> getLinkCheckWarnings(boolean includeInactive, Connection con)
			throws Exception {
		ArrayList<LinkCheckerWarning> warnings = new ArrayList<LinkCheckerWarning>();
		String selectSql = "SELECT id, pattern, is_exact_match, message, is_active FROM LINK_CHECK_WARNINGS ";
		if (includeInactive == false)
			selectSql += "WHERE is_active=1 ";
		selectSql += "ORDER BY id ASC";

		ResultSet rs = null;
		Statement stmt = null;
		try {
			stmt = con.createStatement();
			rs = stmt.executeQuery(selectSql);

			while (rs.next()) {
				LinkCheckerWarning warning = new LinkCheckerWarning(rs.getInt(1), new URI(rs.getString(2)),
						rs.getBoolean(3), rs.getString(4), rs.getBoolean(5));
				warnings.add(warning);
			}

			return warnings;
		} catch (Exception e) {
			throw new Exception("Error retrieving link check warnings", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}

			if (stmt != null) {
				try {
					stmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static int writeLinkCheckWarning(LinkCheckerWarning warning) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return writeLinkCheckWarning(con, warning);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static int writeLinkCheckWarning(Connection con, LinkCheckerWarning warning) throws Exception {
		String updateSql = "UPDATE LINK_CHECK_WARNINGS SET pattern=?, is_exact_match=?, message=?, is_active=? WHERE id=? ";
		String insertSql = "INSERT INTO LINK_CHECK_WARNINGS (pattern, is_exact_match, message, is_active) VALUES (?,?,?,?)";

		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try {
			boolean exists = false;
			if (warning.getId() > 0) {
				LinkCheckerWarning tempWarn = LinkCheckerDatabaseService.getLinkCheckWarning(warning.getId());
				if (tempWarn != null)
					exists = true;
			}

			if (exists) {
				pStmt = con.prepareStatement(updateSql);
				pStmt.setString(1, warning.getPattern().toString());
				pStmt.setBoolean(2, warning.isExactMatch());
				pStmt.setString(3, warning.getMessage());
				pStmt.setBoolean(4, warning.isActive());
				pStmt.setInt(5, warning.getId());
				pStmt.executeUpdate();
			} else {
				pStmt = con.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
				pStmt.setString(1, warning.getPattern().toString());
				pStmt.setBoolean(2, warning.isExactMatch());
				pStmt.setString(3, warning.getMessage());
				pStmt.setBoolean(4, warning.isActive());
				pStmt.executeUpdate();

				rs = pStmt.getGeneratedKeys();
				if (rs.next()) {
					warning.setId(rs.getInt(1));
				}
			}

			return warning.getId();
		} catch (Exception e) {
			throw new Exception("Error writing link checker warning: ", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}

			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static int writeRequest(LinkCheckerRequest request) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			
			return writeRequest(request, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static void queueAllWordpressSites(List<WordpressSite> allSites) throws Exception {
		Connection con = null;
		
		LinkCheckerRequest request = new LinkCheckerRequest();
		request.setIncludePosts(true);
		request.setIncludePrivate(true);
		
		try {
			con = LinkCheckerDatabaseService.getConnection();
			
			for (int i = 0; i < allSites.size(); i++) {
				request.setSiteUrl(new URI(allSites.get(i).getSiteUrl()));
				LinkCheckerDatabaseService.writeRequest(request, con);
				LinkCheckerContext.LOG.debug("adding Wordpress Site:" + request.getSiteUrl());
			}
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static int writeRequest(LinkCheckerRequest request, Connection con) throws Exception {
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try {
			String insertSql = "INSERT INTO LINK_CHECK_QUEUE (site_url, old_site_string, exception_string, include_posts, include_private, email, priority, scan_status, insert_date, error_msg) VALUES (?,?,?,?,?,?,?,?,?,?)";
			String updateSql = "UPDATE LINK_CHECK_QUEUE SET site_url=?, old_site_string=?, exception_string=?, include_posts=?, include_private=?, email=?, priority=?, scan_status=?, insert_date=?, error_msg=? WHERE id=?";

			// check for an existing request
			boolean exists = false;
			if (request.getId() != -1) {
				LinkCheckerRequest tempRequest = LinkCheckerDatabaseService.getRequest(request.getId(), con);
				if (tempRequest != null && tempRequest.getId() != -1)
					exists = true;
			}

			if (exists) {
				pStmt = con.prepareStatement(updateSql);
				pStmt.setString(1, request.getSiteUrl().toString());
				String oldSiteString = "";
				for (LinkCheckerConditional cond : request.getConditionals())
					oldSiteString += cond.getPattern().toString() + "|";
				pStmt.setString(2, oldSiteString);
				String exceptionString = "";
				for (URI exception : request.getConditionalExcludes())
					exceptionString += exception.toString();
				pStmt.setString(3, exceptionString);
				pStmt.setBoolean(4, request.isIncludePosts());
				pStmt.setBoolean(5, request.isIncludePrivate());
				pStmt.setString(6, request.getEmailAddress());
				pStmt.setInt(7, request.getPriority());
				pStmt.setInt(8, request.getStatusCode());
				pStmt.setLong(9, request.getInsertDate().getTime());
				if (request.getErrorMessage().length() > 1000)
					pStmt.setString(10, request.getErrorMessage().substring(0, 1000));
				else
					pStmt.setString(10, request.getErrorMessage());
				pStmt.setInt(11, request.getId());
				pStmt.executeUpdate();

				return request.getId();
			} else {
				// we write a new request
				pStmt = con.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
				pStmt.setString(1, request.getSiteUrl().toString());
				String oldSiteString = "";
				for (LinkCheckerConditional cond : request.getConditionals())
					oldSiteString += cond.getPattern().toString() + "|";
				pStmt.setString(2, oldSiteString);
				String exceptionString = "";
				for (URI exception : request.getConditionalExcludes())
					exceptionString += exception.toString();
				pStmt.setString(3, exceptionString);
				pStmt.setBoolean(4, request.isIncludePosts());
				pStmt.setBoolean(5, request.isIncludePrivate());
				pStmt.setString(6, request.getEmailAddress());
				pStmt.setInt(7, request.getPriority());
				pStmt.setInt(8, request.getStatusCode());
				pStmt.setLong(9, request.getInsertDate().getTime());
				if (request.getErrorMessage().length() > 1000)
					pStmt.setString(10, request.getErrorMessage().substring(0, 1000));
				else
					pStmt.setString(10, request.getErrorMessage());
				pStmt.executeUpdate();

				rs = pStmt.getGeneratedKeys();
				rs.next();
				return rs.getInt(1);
			}
		} catch (Exception e) {
			throw new Exception(e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerRequest getRunningRequest() throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getRunningRequest(con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerRequest getRunningRequest(Connection con) throws Exception {
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try {
			String sql = "SELECT TOP 1 id FROM LINK_CHECK_QUEUE WHERE scan_status=? ";

			pStmt = con.prepareStatement(sql);
			pStmt.setInt(1, LinkCheckerContext.STATUS_STARTED);
			rs = pStmt.executeQuery();

			if (rs.next())
				return LinkCheckerDatabaseService.getRequest(rs.getInt(1), con);
			else
				return null;
		} catch (Exception e) {
			throw new Exception("Error getting current request to link checker queue: ", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerRequest getNextRequest() throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getNextRequest(con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static LinkCheckerRequest getNextRequest(Connection con) throws Exception {
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try {
			String sql = "SELECT TOP 1 id FROM LINK_CHECK_QUEUE WHERE scan_status=? ORDER BY priority DESC, insert_date ASC";

			pStmt = con.prepareStatement(sql);
			pStmt.setInt(1, LinkCheckerContext.STATUS_NOT_STARTED);
			rs = pStmt.executeQuery();

			if (rs.next())
				return LinkCheckerDatabaseService.getRequest(rs.getInt(1), con);
			else
				return null;
		} catch (Exception e) {
			throw new Exception("Error getting next request in link checker queue: ", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerRequest getRequest(int requestId) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getRequest(requestId, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static LinkCheckerRequest getRequest(int requestId, Connection con) throws Exception {
		PreparedStatement pStmt = null;
		ResultSet rs = null;

		try {
			String sql = "SELECT site_url, old_site_string, exception_string, include_posts, include_private, email, priority, scan_status, insert_date, error_msg FROM LINK_CHECK_QUEUE WHERE id=?";

			pStmt = con.prepareStatement(sql);
			pStmt.setInt(1, requestId);
			rs = pStmt.executeQuery();

			if (rs.next()) {
				LinkCheckerRequest request = new LinkCheckerRequest();
				request.setId(requestId);
				request.setSiteUrl(new URI(rs.getString(1)));
				// create our array of old sites
				String oldSiteString = rs.getString(2);
				if (oldSiteString != null && oldSiteString.length() > 0) {
					// create our list of conditionals to flag for warnings
					ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
					String[] oldSiteBits = oldSiteString.split("\\|");
					LinkCheckerConditional tempConditional = null;
					for (int i = 0; i < oldSiteBits.length; i++) {
						try {
							tempConditional = new LinkCheckerConditional(
									LinkCheckerContext.formatSiteUrl(new URI(oldSiteBits[i])),
									LinkCheckerContext.STATUS_OLD_SITE);
							conditionals.add(tempConditional);
						} catch (Exception report) {
							LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site URL.",
									report);
						}
					}
					request.setConditionals(conditionals);
				}

				String exceptionString = rs.getString(3);
				if (exceptionString != null && exceptionString.length() > 0) {
					ArrayList<URI> exceptionSites = new ArrayList<URI>();

					String[] exceptionSiteBits = exceptionString.split("\\|");
					for (int i = 0; i < exceptionSiteBits.length; i++) {
						try {
							exceptionSites.add(LinkCheckerContext.formatLinkUrl(new URI(exceptionSiteBits[i])));
						} catch (Exception report) {
							LinkCheckerContext.LOG.error(
									"Error in LinkCheckerRequest URL.  Ignoring Old Site Exception URL.", report);
						}
					}
					request.setConditionalExcludes(exceptionSites);
				}
				request.setIncludePosts(rs.getBoolean(4));
				request.setIncludePrivate(rs.getBoolean(5));
				request.setEmailAddress(rs.getString(6));
				request.setPriority(rs.getInt(7));
				request.setStatusCode(rs.getInt(8));
				request.setInsertDate(new Date(rs.getLong(9)));
				request.setErrorMessage(rs.getString(10));
				return request;
			} else
				return null;
		} catch (Exception e) {
			throw new Exception("Error getting next request in link checker queue: ", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	public static LinkCheckerRequest getQueuedRequest(URI siteUrl) throws Exception {
		Connection con = null;
		try {
			con = LinkCheckerDatabaseService.getConnection();
			return getQueuedRequest(siteUrl, con);
		} catch (Exception e) {
			throw e;
		} finally {
			if (con != null) {
				try {
					con.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}

	private static LinkCheckerRequest getQueuedRequest(URI siteUrl, Connection con) throws Exception {
		PreparedStatement pStmt = null;
		ResultSet rs = null;
		siteUrl = LinkCheckerContext.formatSiteUrl(siteUrl);
		try {
			String sql = "SELECT id, old_site_string, exception_string, include_posts, include_private, email, priority, insert_date, error_msg FROM LINK_CHECK_QUEUE WHERE site_url=? AND scan_status=? AND priority > 0";

			pStmt = con.prepareStatement(sql);
			pStmt.setString(1, siteUrl.toString());
			pStmt.setInt(2, LinkCheckerContext.STATUS_NOT_STARTED);
			rs = pStmt.executeQuery();
			if (rs.next()) {
				LinkCheckerRequest request = new LinkCheckerRequest();
				request.setId(rs.getInt(1));
				request.setSiteUrl(siteUrl);
				// create our array of old sites
				String oldSiteString = rs.getString(2);
				if (oldSiteString != null && oldSiteString.length() > 0) {
					// create our list of conditionals to flag for warnings
					ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
					String[] oldSiteBits = oldSiteString.split("\\|");
					LinkCheckerConditional tempConditional = null;
					for (int i = 0; i < oldSiteBits.length; i++) {
						try {
							tempConditional = new LinkCheckerConditional(
									LinkCheckerContext.formatSiteUrl(new URI(oldSiteBits[i])),
									LinkCheckerContext.STATUS_OLD_SITE);
							conditionals.add(tempConditional);
						} catch (Exception report) {
							LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site URL.",
									report);
						}
					}
					request.setConditionals(conditionals);
				}

				String exceptionString = rs.getString(3);
				if (exceptionString != null && exceptionString.length() > 0) {
					ArrayList<URI> exceptionSites = new ArrayList<URI>();

					String[] exceptionSiteBits = exceptionString.split("\\|");
					for (int i = 0; i < exceptionSiteBits.length; i++) {
						try {
							exceptionSites.add(LinkCheckerContext.formatLinkUrl(new URI(exceptionSiteBits[i])));
						} catch (Exception report) {
							LinkCheckerContext.LOG.error(
									"Error in LinkCheckerRequest URL.  Ignoring Old Site Exception URL.", report);
						}
					}
					request.setConditionalExcludes(exceptionSites);
				}
				request.setIncludePosts(rs.getBoolean(4));
				request.setIncludePrivate(rs.getBoolean(5));
				request.setEmailAddress(rs.getString(6));
				request.setPriority(rs.getInt(7));
				request.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
				request.setInsertDate(new Date(rs.getLong(8)));
				request.setErrorMessage(rs.getString(9));
				return request;
			} else
				return null;
		} catch (Exception e) {
			throw new Exception("Error getting next request in link checker queue: ", e);
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
			if (pStmt != null) {
				try {
					pStmt.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error(report);
				}
			}
		}
	}
}
