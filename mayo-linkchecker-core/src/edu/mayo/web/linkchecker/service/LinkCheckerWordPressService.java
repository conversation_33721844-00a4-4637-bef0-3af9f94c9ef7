package edu.mayo.web.linkchecker.service;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import edu.mayo.web.linkchecker.LinkCheckerPage;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.wordpress.WordpressDatabaseUtils;
import edu.mayo.web.linkchecker.wordpress.WordpressPost;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;


public class LinkCheckerWordPressService {
	
	// get the list of all WordPress Blogs from the WP DB
	public static List<WordpressSite> getAllSites() throws Exception {
		List<WordpressSite> sites = new ArrayList<WordpressSite>();
		return WordpressDatabaseUtils.getAllWordpressSites();
	}
	
	// make XMLRPC calls to WordPress to get page details
	public static List<LinkCheckerPage> getWordPressPages(LinkCheckerSite site, boolean isPublishedOnly) throws Exception {
		List<LinkCheckerPage> pages = new ArrayList<LinkCheckerPage>();
		
		List<WordpressPost> wpPosts = new ArrayList<WordpressPost>(); 
		
		
		wpPosts = WordpressDatabaseUtils.getPosts(site.getWpSite(), isPublishedOnly);
		
		for (WordpressPost post : wpPosts) {
			LinkCheckerPage tempPage = new LinkCheckerPage(new URI(post.getUrl()));
			tempPage.setWpPost(post);
			tempPage.setSiteId(site.getId());
			pages.add(tempPage);
		}
				
		return pages;
	}

}
