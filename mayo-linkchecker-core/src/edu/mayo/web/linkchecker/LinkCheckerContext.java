package edu.mayo.web.linkchecker;

import java.io.InputStream;
import java.net.URI;
import java.util.Hashtable;
import java.util.List;
import java.util.Properties;

import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.apache.commons.io.IOUtils;
import org.apache.http.auth.NTCredentials;
import org.apache.log4j.Logger;
import org.apache.log4j.PropertyConfigurator;

import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;

public class LinkCheckerContext {

	private static final String PROPS_FILE = "linkchecker.properties";
	public static Properties DB_PROPS = new Properties();
	public static Properties props = new Properties();

	// initialize the checkedLinksCache hash table. This saves us several page
	// loads.
	private static Hashtable<String, LinkCheckerLinkStatus> checkedLinksCache = new Hashtable<String, LinkCheckerLinkStatus>();
	public static String REPORT_HEADER_FILE = null;
	public static String REPORT_HEADER = null;
	// need NT_CREDENTIAL for generic NTLM auth schemes
	public static NTCredentials NT_CREDENTIALS = null;

	// init logger
	public static Logger LOG = Logger.getLogger(LinkCheckerContext.class);

	// PRIORITY values
	public static final int PRIORITY_LOW = 0;
	public static final int PRIORITY_MEDIUM = 1;
	public static final int PRIORITY_HIGH = 2;

	// STATUS properties
	public static final int STATUS_DEFAULT_ERROR = 1;
	public static final int STATUS_404 = 2;
	public static final int STATUS_401 = 3;
	public static final int STATUS_400 = 4;
	public static final int STATUS_NOT_FOUND = 5;
	public static final int STATUS_OLD_SITE = 6;
	public static final int STATUS_BAD_FORMAT = 7;
	public static final int STATUS_WARNING_FLAGGED = 8;
	public static final int STATUS_OK = 9;
	public static final int STATUS_NOT_STARTED = 10;
	public static final int STATUS_STARTED = 11;
	public static final int STATUS_CLOSED = 12;
	public static final int STATUS_ALREADY_QUEUED = 13;

	public static Hashtable<Integer, String> STATUS_MESSAGES = new Hashtable<Integer, String>();

	static {
		System.setProperty("log4j.debug", "true");
		try {
			InputStream in = LinkCheckerContext.getResourceStream(PROPS_FILE);
			props.load(in);
			System.out.println("Properties file loaded: " + PROPS_FILE + ":size=" + props.size());

			// initialize the log4j connection
			PropertyConfigurator.configure(props);
			// PropertyConfigurator.configure(propsFile.getAbsolutePath());

			// initialize the DB connection properties
			// we do this in the static constructor to avoid any race conditions
			// when accessing static variables
			DB_PROPS.put("integratedSecurity", "false");
			DB_PROPS.put("portNumber", props.getProperty("edu.mayo.web.db.port"));
			DB_PROPS.put("databaseName", props.getProperty("edu.mayo.web.db.name"));
			DB_PROPS.put("instanceName", props.getProperty("edu.mayo.web.db.instance.name"));
			DB_PROPS.put("userName", props.getProperty("edu.mayo.web.linkchecker.username"));
			DB_PROPS.put("password", props.getProperty("edu.mayo.web.linkchecker.password"));

			// init the STATUS_MESSAGE queue
			STATUS_MESSAGES.put(STATUS_DEFAULT_ERROR,
					props.getProperty("edu.mayo.web.linkchecker.status.default.error"));
			STATUS_MESSAGES.put(STATUS_404, props.getProperty("edu.mayo.web.linkchecker.status.404"));
			STATUS_MESSAGES.put(STATUS_401, props.getProperty("edu.mayo.web.linkchecker.status.401"));
			STATUS_MESSAGES.put(STATUS_400, props.getProperty("edu.mayo.web.linkchecker.status.400"));
			STATUS_MESSAGES.put(STATUS_NOT_FOUND, props.getProperty("edu.mayo.web.linkchecker.status.file.not.found"));
			STATUS_MESSAGES.put(STATUS_OLD_SITE, props.getProperty("edu.mayo.web.linkchecker.status.old.site"));
			STATUS_MESSAGES.put(STATUS_BAD_FORMAT, props.getProperty("edu.mayo.web.linkchecker.status.bad.format"));
			STATUS_MESSAGES.put(STATUS_WARNING_FLAGGED,
					props.getProperty("edu.mayo.web.linkchecker.status.warning.flagged"));
			STATUS_MESSAGES.put(STATUS_OK, props.getProperty("edu.mayo.web.linkchecker.status.ok"));
			STATUS_MESSAGES.put(STATUS_NOT_STARTED, props.getProperty("edu.mayo.web.linkchecker.status.not.started"));
			STATUS_MESSAGES.put(STATUS_STARTED, props.getProperty("edu.mayo.web.linkchecker.status.started"));
			STATUS_MESSAGES.put(STATUS_CLOSED, props.getProperty("edu.mayo.web.linkchecker.status.closed"));

			// init the NT_CREDENTIAL for the HttpClient calls
			NT_CREDENTIALS = new NTCredentials(props.getProperty("edu.mayo.web.linkchecker.username"),
					props.getProperty("edu.mayo.web.linkchecker.password"), "",
					props.getProperty("edu.mayo.web.linkchecker.mfad.string"));

			// load the report template;
			REPORT_HEADER_FILE = props.getProperty("edu.mayo.web.linkchecker.report.header.file");
			InputStream reportIn = LinkCheckerContext.getResourceStream(REPORT_HEADER_FILE);
			REPORT_HEADER = IOUtils.toString(reportIn, "UTF-8");

		} catch (Exception e) {
			LinkCheckerContext.LOG.error("Error initializing LinkChecker Properties: ", e);
		}
	}

	public static final int LINK_CHECKER_REQUEST_TIMEOUT = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.request.timeout"));
	public static final int LINK_CHECKER_CONNECTION_TIMEOUT = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.connection.timeout"));
	public static final int LINK_CHECKER_SOCKET_TIMEOUT = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.socket.timeout"));
	public static final int LINK_CHECKER_CONNECTION_POOL_SIZE = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.connection.pool.size"));
	public static final String LINK_CHECKER_PROXY = props.getProperty("edu.mayo.web.linkchecker.proxy");
	public static final String LINK_CHECKER_USER_AGENT = props.getProperty("edu.mayo.web.linkchecker.user.agent");
	public static final String LINK_CHECKER_ACCEPTS = props.getProperty("edu.mayo.web.linkchecker.accepts");
	public static final String LINK_CHECKER_ACCEPTS_LANGUAGE = props
			.getProperty("edu.mayo.web.linkchecker.accepts.language");
	public static final String LINK_CHECKER_LINK_SELECTOR = props.getProperty("edu.mayo.web.linkchecker.link.selector");
	public static final String LINK_CHECKER_MFAD_STRING = props.getProperty("edu.mayo.web.linkchecker.mfad.string");
	public static final String LINK_CHECKER_MAYO_DOMAIN = props.getProperty("edu.mayo.web.linkchecker.mayo.domain");
	public static final String LINK_CHECKER_SUPPORTED_ENCRYPTION = props.getProperty("edu.mayo.web.linkchecker.supported.encription");
	public static String LINK_CHECKER_KEY_STORE_PATH = props.getProperty("edu.mayo.web.linkchecker.key.store.path");
	public static final String LINK_CHECKER_CERTIFICATES_PATH = props
			.getProperty("edu.mayo.web.linkchecker.certificates.path");
	public static final String LINK_CHECKER_KEY_STORE_PASSWORD = props
			.getProperty("edu.mayo.web.linkchecker.key.store.password");
	public static final Boolean LINK_CHECKER_USE_STATUS_CACHE = Boolean
			.parseBoolean(props.getProperty("edu.mayo.web.linkchecker.use.status.cache"));
	public static final long LINK_CHECKER_RETRY_WAIT = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.retry.wait"));
	public static final int LINK_CHECKER_REPORT_MAX_LINK_TEXT_LENGTH = Integer
			.parseInt(props.getProperty("edu.mayo.web.linkchecker.report.max.link.text.length"));
	public static final String LINK_CHECKER_REPORT_DIRECTORY= props.getProperty("edu.mayo.web.linkchecker.report.directory");
	public static final String DB_CONNECTION_STRING = props.getProperty("edu.mayo.web.db.connection.string");
	public static final String DB_DRIVER = props.getProperty("edu.mayo.web.db.jdbc.driver");
	public static final int DB_MAX_URL_LENGTH = Integer.parseInt(props.getProperty("edu.mayo.web.db.max.url.length"));
	public static final int DB_MAX_TAG_LENGTH = Integer.parseInt(props.getProperty("edu.mayo.web.db.max.tag.length"));
	public static final int DB_MAX_CAPTION_LENGTH = Integer
			.parseInt(props.getProperty("edu.mayo.web.db.max.caption.length"));

	public static final String SMTP_HOST = props.getProperty("edu.mayo.web.smtp.host");
	public static final int SMTP_PORT = Integer.parseInt(props.getProperty("edu.mayo.web.smtp.port"));
	public static final String SMTP_FROM_ADDRESS = props.getProperty("edu.mayo.web.smtp.from.address");
	public static final String SMTP_ERROR_TO_ADDRESS = props.getProperty("edu.mayo.web.smtp.error.to.address");

	public static final String WP_URL = props.getProperty("edu.mayo.web.wordpress.url");	
	public static final String WP_LOGIN = props.getProperty("edu.mayo.web.wordpress.login");
	public static final String WP_HOSTNAME_SHORT = props.getProperty("edu.mayo.web.wordpress.hostname.short");
	public static final String WP_HOSTNAME_FULL = props.getProperty("edu.mayo.web.wordpress.hostname.full");
	public static final String WP_ADMIN_URL = props.getProperty("edu.mayo.web.wordpress.admin.url");
	public static final String WP_LOGIN_URL = props.getProperty("edu.mayo.web.wordpress.login.url");
	public static final String WP_ADMIN_NAME = props.getProperty("edu.mayo.web.wordpress.admin.name");
	public static final String WP_ADMIN_PASSWORD = props.getProperty("edu.mayo.web.wordpress.admin.password");
	public static final String WP_DB_SERVER = props.getProperty("edu.mayo.web.wordpress.db.server");
	public static final String WP_DB_PORT = props.getProperty("edu.mayo.web.wordpress.db.port");
	public static final String WP_DB_INSTANCE = props.getProperty("edu.mayo.web.wordpress.db.instance");
	public static final String WP_DB_USER = props.getProperty("edu.mayo.web.wordpress.db.user");
	public static final String WP_DB_PASSWORD = props.getProperty("edu.mayo.web.wordpress.db.password");

	public static URI formatSiteUrl(URI url) {
		try {
			String pathFix = "", hostFix = "";

			if (!url.getPath().endsWith("/") || url.getHost().indexOf(".") < 0) {
				if (!url.getPath().endsWith("/"))
					pathFix = "/";
				if (url.getHost().indexOf(".") < 0)
					hostFix = "." + LinkCheckerContext.LINK_CHECKER_MAYO_DOMAIN;
				url = new URI(url.getScheme(), url.getUserInfo(), url.getHost() + hostFix, url.getPort(),
						url.getPath() + pathFix, url.getQuery(), url.getFragment());
			}

			if (url.toString().length() > LinkCheckerContext.DB_MAX_URL_LENGTH)
				url = new URI(url.toString().substring(0, LinkCheckerContext.DB_MAX_URL_LENGTH));
		} catch (Exception ignore) {
		}

		return url.normalize();
	}

	public static URI formatLinkUrl(URI url) {
		try {
			if (url.toString().length() > LinkCheckerContext.DB_MAX_URL_LENGTH)
				url = new URI(url.toString().substring(0, LinkCheckerContext.DB_MAX_URL_LENGTH));
			// if we don't have a domain, we add the mayo domain to standardize
			if (url.getHost().indexOf(".") < 0)
				url = new URI(url.getScheme(), url.getUserInfo(),
						url.getHost() + "." + LinkCheckerContext.LINK_CHECKER_MAYO_DOMAIN, url.getPort(), url.getPath(),
						url.getQuery(), url.getFragment());
		} catch (Exception e) {

		}
		return url;
	}

	public static InputStream getResourceStream(String path) throws Exception {
		InputStream in = LinkCheckerContext.class.getClassLoader().getSystemResourceAsStream(path);
		if (in == null)
			in = ClassLoader.getSystemResourceAsStream(path);
		if (in == null)
			in = LinkCheckerContext.class.getClassLoader().getResourceAsStream(path);
		return in;
	}

	// methods for handling the checked links cache
	public static void addToCheckedLinksCache(URI link, LinkCheckerLinkStatus status) {
		synchronized (checkedLinksCache) {
			if (LinkCheckerContext.LINK_CHECKER_USE_STATUS_CACHE)
				checkedLinksCache.put(link.normalize().toString(), status);
		}
	}

	public static LinkCheckerLinkStatus getCachedLinkStatus(URI link) {
		synchronized (checkedLinksCache) {
			return checkedLinksCache.get(link.normalize().toString());
		}
	}

	public static void clearCheckedLinksCache() {
		synchronized (checkedLinksCache) {
			checkedLinksCache = new Hashtable<String, LinkCheckerLinkStatus>();
		}
	}

	// init the link checker warnings out of the DB
	public static List<LinkCheckerWarning> LINK_CHECKER_WARNINGS = LinkCheckerContext.initWarnings();

	private static List<LinkCheckerWarning> initWarnings() {
		List<LinkCheckerWarning> warnings = null;
		try {
			warnings = LinkCheckerDatabaseService.getLinkCheckWarnings(false);
		} catch (Exception report) {
			LinkCheckerContext.LOG.error("Unable to load data from LINK_CHECKER_WARNINGS table", report);
		}
		return warnings;
	}

	public static void sendEmail(String toAddress, String subject, String htmlContent) throws Exception {
		// format a receipt in HTML, create a message & send to the customer
		Properties emailProps = new Properties();
		emailProps.setProperty("mail.transport.protocol", "smtp");
		emailProps.setProperty("mail.smtp.host", LinkCheckerContext.SMTP_HOST);
		emailProps.setProperty("mail.smtp.port", Integer.toString(LinkCheckerContext.SMTP_PORT));

		Session mailSession = Session.getDefaultInstance(emailProps, null);
		Transport transport = mailSession.getTransport();

		// create the message
		MimeMessage message = new MimeMessage(mailSession);
		message.setContent(htmlContent, "text/html; charset=\"UTF-8\"");
		message.addRecipient(Message.RecipientType.TO, new InternetAddress(toAddress));
		message.setSubject(subject);
		message.setFrom(new InternetAddress(LinkCheckerContext.SMTP_FROM_ADDRESS));

		transport.connect();
		transport.sendMessage(message, message.getRecipients(Message.RecipientType.TO));
		transport.close();
	}
}
