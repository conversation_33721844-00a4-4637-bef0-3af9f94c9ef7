package edu.mayo.web.linkchecker.wordpress;

import java.util.ArrayList;
import java.util.List;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.jsoup.select.Selector;

public class WordpressPost {
	
	private String postId = "",
				   postContent = "",
				   guid = "",
				   postName = "",
				   url = "";
	
	private WordpressSite site = null;
	
	public String getPostName() {
		return postName;
	}
	public void setPostName(String postName) {
		this.postName = postName;
	}

	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}

	public String getPostId() {
		return postId;
	}
	public void setPostId(String postId) {
		this.postId = postId;
	}
	public String getPostContent() {
		return postContent;
	}
	public void setPostContent(String postContent) {
		this.postContent = postContent;
	}
	public WordpressSite getSite() {
		return site;
	}
	public void setSite(WordpressSite site) {
		this.site = site;
	}
	public String getGuid() {
		return guid;
	}
	public void setGuid(String guid) {
		this.guid = guid;
	}
	
	public List<String> searchForReferences(String postContent, List<String> searchUrls) {
		ArrayList<String> references = new ArrayList<String>();
		
		//parse content to get external references
		Document doc = Jsoup.parse(postContent);
		
		// get the links from whatever content areas are returned
		Elements allLinks = Selector.select("a[href], [src], link[href]", doc);

		// get the links from whatever content areas are returned
//		allLinks = Selector.select("iframe[src], img[src]", doc);

		// iterate through the links and normalize/standardize the URL
		// we do a lot of 'fixing' URLs to match IE's loose
		// standards--especially on 'file://' links
		for (Element pageElement : allLinks) {
			String linkTarget = pageElement.attr("href");
			if (linkTarget != null)
				linkTarget = linkTarget.toLowerCase().trim();
			else {
				linkTarget = pageElement.attr("src");
				if (linkTarget != null)
					linkTarget = linkTarget.toLowerCase().trim();
			}
			
			if (linkTarget != null && linkTarget.length() != 0) {
				for (String searchUrl: searchUrls) {
					if (linkTarget.toLowerCase().startsWith(searchUrl.toLowerCase()))
						references.add(linkTarget);
				}				
			}
		}

		return references;
	}
	
}
