package edu.mayo.web.linkchecker.wordpress;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.jsoup.select.Selector;

import edu.mayo.web.linkchecker.LinkCheckerContext;

public class WordpressDatabaseUtils {
	
	public static Connection getConnection() throws Exception {
		String conString = "jdbc:mariadb://"+LinkCheckerContext.WP_DB_SERVER+
				":"+LinkCheckerContext.WP_DB_PORT+"/"+LinkCheckerContext.WP_DB_INSTANCE+
				"?user="+LinkCheckerContext.WP_DB_USER+
				"&password="+LinkCheckerContext.WP_DB_PASSWORD;	
		Connection con = DriverManager.getConnection(conString);
		return con;
	}
	
	// get the list of all WordPress Blogs from the WP DB
	public static List<WordpressSite> getAllWordpressSites() throws Exception {
		List<WordpressSite> sites = new ArrayList<WordpressSite>();
		Connection con = null;
		try {
			con = WordpressDatabaseUtils.getConnection();
			sites = WordpressDatabaseUtils.getAllWordpressSites(con);
		} catch (Exception e) {
			throw e;
		} finally {
			try {
				con.close();
			} catch (Exception ignored) {}
		}
		return sites;
	}
	
	public static List<WordpressSite> getAllWordpressSites(Connection con) throws Exception {
		ArrayList<WordpressSite> sites = new ArrayList<WordpressSite>();
		String SELECT_SITES_SQL = "SELECT blog_id, path FROM wp_blogs where archived <> '1' ORDER BY blog_id ASC";
		//connect to WP and get site list as WordpressSite array
		Statement stmt = con.createStatement();			
		ResultSet rs = stmt.executeQuery(SELECT_SITES_SQL);
		String hostName = LinkCheckerContext.WP_URL.substring(0, LinkCheckerContext.WP_URL.lastIndexOf("/"));
//		hostName = hostName.substring(0, hostName.lastIndexOf("/"));
		
		while (rs.next()) {
			WordpressSite tempSite = new WordpressSite();
			tempSite.setBlogId(rs.getString(1));
			tempSite.setSiteUrl(hostName+rs.getString(2));
			sites.add(tempSite);
		}
		
		return sites;
	}
	
	// get the list of all WordPress Blogs from the WP DB
	public static WordpressSite getWordpressSite(String url) throws Exception {
		Connection con = null;
		try {
			con = WordpressDatabaseUtils.getConnection();
			return WordpressDatabaseUtils.getWordpressSite(url, con);
		} catch (Exception e) {
			throw e;
		} finally {
			try {
				con.close();
			} catch (Exception ignored) {}
		}
	}
	
	public static WordpressSite getWordpressSite(String url, Connection con) throws Exception {
		
		String sitePath = url.substring(LinkCheckerContext.WP_URL.lastIndexOf("/"));
		String SELECT_SITES_SQL = "SELECT blog_id, path FROM wp_blogs WHERE path='"+sitePath+"'";
		WordpressSite site = null;
		//connect to WP and get site list as WordpressSite array
		Statement stmt = con.createStatement();			
		ResultSet rs = stmt.executeQuery(SELECT_SITES_SQL);
		if (rs.next()) {
			site = new WordpressSite();
			site.setBlogId(rs.getString(1));
			site.setSiteUrl(url);
		}			
		
		return site;
	}
	
	// get the list of all WordPress Blogs from the WP DB
	public static List<WordpressPost> getPosts(WordpressSite site, boolean isPublishedOnly) throws Exception {
		Connection con = null;
		try {
			con = WordpressDatabaseUtils.getConnection();
			return WordpressDatabaseUtils.getPosts(site, isPublishedOnly, con);
		} catch (Exception e) {
			throw e;
		} finally {
			try {
				con.close();
			} catch (Exception ignored) {}
		}
	}	
	
	public static List<WordpressPost> getPosts(WordpressSite site, boolean isPublishedOnly, Connection con) throws Exception {
		ArrayList<WordpressPost> posts = new ArrayList<WordpressPost>();
		
		//get a list of posts
		String SELECT_SQL = "SELECT id, post_content, guid, post_name FROM wp_"+site.getBlogId()+"_posts "+
							"where (post_type='page' OR post_type='post') AND ";
		if (isPublishedOnly)
			SELECT_SQL += "post_status = 'publish'";
		else
			SELECT_SQL += " (post_status = 'publish' OR post_status='private')";
		Statement stmt = con.createStatement();
		ResultSet result = stmt.executeQuery(SELECT_SQL);
		
		
		//iterate over the list of posts to load them
		while (result.next()) {
			WordpressPost post = new WordpressPost();
			post.setPostId(result.getString(1));
			post.setPostContent(result.getString(2));
			post.setSite(site);
			post.setGuid(result.getString(3));
			post.setPostName(result.getString(4));
			String url = site.getSiteUrl()+post.getPostName();
			if (!url.endsWith("/"))
				url += "/";
			post.setUrl(url);
System.out.println("POST URL: "+post.getUrl());			
			posts.add(post);
		}
		
		return posts;
	}
	
}
