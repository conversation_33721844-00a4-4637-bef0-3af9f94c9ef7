package edu.mayo.web.linkchecker;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.CodingErrorAction;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.Consts;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.client.AuthCache;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.auth.NTLMScheme;
import org.apache.http.impl.client.BasicAuthCache;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;

public class LinkCheckerHttpClient {

	// create HTTP client object
	private static CloseableHttpClient httpClient = null;
	// create static instances of the WordPress and default contexts
	private static HttpClientContext wordPressContext = null;
	private static HttpClientContext wordPressAuthenticatedContext = null;
	private static HttpClientContext defaultContext = null;
	// create static RequestConfig object
	public static RequestConfig requestConfig = null;
	// create our trust manager object
	public static TrustManager trustManager = null;

	static {
		// create static RequestConfig object
		initDefaultRequestConfig();
		// create our trust manager object
		initTrustManager();
		// create HTTP client object
		initHttpClient();
		// create static instances of the wordpress and default contexts
		defaultContext = HttpClientContext.create();
		initWordPressContext();
		initWordPressAuthenticatedContext();
	}

	public static CloseableHttpClient getHttpClient() {
		return httpClient;
	}

	private static void initHttpClient() {
		try {

			// SSL context for secure connections can be created either based on
			// system or application specific properties.
			KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
			FileInputStream instream = new FileInputStream(new File(LinkCheckerContext.LINK_CHECKER_KEY_STORE_PATH));
			try {
				ks.load(instream, LinkCheckerContext.LINK_CHECKER_KEY_STORE_PASSWORD.toCharArray());
			} finally {
				instream.close();
			}

			// Here is where we load any other certificates the linkHandlers
			// need/provide
			int counter = 0;
			File certificatesDir = new File(LinkCheckerContext.LINK_CHECKER_CERTIFICATES_PATH);
			if (certificatesDir.exists()) {
				for (File file : certificatesDir.listFiles()) {
					if (file.isFile() && file.getName().toLowerCase().endsWith(".cer")) {
						X509Certificate cert = readX509Certificate(file.getCanonicalPath());
						LinkCheckerContext.LOG.debug("loading certificate: " + file.getCanonicalPath());
						ks.setCertificateEntry("linkchecker_" + (counter++), cert);
					}
				}
			}

			SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(ks, new TrustSelfSignedStrategy()).build();
			
			String supportedEncriptionStr = LinkCheckerContext.LINK_CHECKER_SUPPORTED_ENCRYPTION;
			String[] supportedTLS = supportedEncriptionStr.split(",");
//			String[] supportedTLS = new String[] { "TLSv1","TLSv1.1","TLSv1.2"};
//			String[] supportedTLS = new String[] { "TLSv1"};
			
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, supportedTLS,
					null, SSLConnectionSocketFactory.getDefaultHostnameVerifier());

			Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
					.register("https", sslsf).register("http", new PlainConnectionSocketFactory()).build();

			// Create connection configuration
			ConnectionConfig connectionConfig = ConnectionConfig.custom()
					.setMalformedInputAction(CodingErrorAction.IGNORE)
					.setUnmappableInputAction(CodingErrorAction.IGNORE).setCharset(Consts.UTF_8).build();

			// create the thread-safe connection pool
			PoolingHttpClientConnectionManager poolManager = new PoolingHttpClientConnectionManager(
					socketFactoryRegistry);
			poolManager.setDefaultConnectionConfig(connectionConfig);
			poolManager.setMaxTotal(200);
			poolManager.setDefaultMaxPerRoute(100);

			LinkCheckerHttpClient.httpClient = HttpClients.custom()
					// .setSSLSocketFactory(sslsf)
					.setRedirectStrategy(LaxRedirectStrategy.INSTANCE)
					.setUserAgent(LinkCheckerContext.LINK_CHECKER_USER_AGENT).setConnectionManager(poolManager)
					.setDefaultRequestConfig(LinkCheckerHttpClient.requestConfig).build();
		} catch (Exception e) {
			LinkCheckerContext.LOG.error("Error creating HttpClient", e);
		}
	}

	private static X509Certificate readX509Certificate(String certFile) throws Exception {
		// load any certificates we need from the file system
		InputStream in = null;
		try {
			// try various methods to read the file
			in = new FileInputStream(certFile);

			// read the bytes
			byte value[] = new byte[in.available()];
			in.read(value);
			ByteArrayInputStream bais = new ByteArrayInputStream(value);

			// get X509 certificate factory
			CertificateFactory certFactory = CertificateFactory.getInstance("X.509");

			// certificate factory can now create the certificate
			return (X509Certificate) certFactory.generateCertificate(bais);
		} catch (Exception e) {
			throw e;
		} finally {
			if (in != null)
				in.close();
		}
	}

	public static HttpClientContext getAuthenticatedHttpClientContext(URI link) throws Exception {
		boolean hasDomain = (link.getHost().indexOf('.') != -1);

		if (link.getHost().equalsIgnoreCase(LinkCheckerContext.WP_HOSTNAME_SHORT)
				|| link.getHost().equalsIgnoreCase(LinkCheckerContext.WP_HOSTNAME_FULL)) {
			return LinkCheckerHttpClient.wordPressAuthenticatedContext;
		} else if (link.getHost().toLowerCase().endsWith(LinkCheckerContext.LINK_CHECKER_MAYO_DOMAIN) || !hasDomain) {
			return LinkCheckerHttpClient.getMayoBasicContext(link);
		} else {
			return LinkCheckerHttpClient.defaultContext;
		}
	}

	public static HttpClientContext getHttpClientContext(URI link) throws Exception {
		boolean hasDomain = (link.getHost().indexOf('.') != -1);

		if (link.getHost().equalsIgnoreCase(LinkCheckerContext.WP_HOSTNAME_SHORT)
				|| link.getHost().equalsIgnoreCase(LinkCheckerContext.WP_HOSTNAME_FULL)) {
			return LinkCheckerHttpClient.wordPressContext;
		} else if (link.getHost().toLowerCase().endsWith(LinkCheckerContext.LINK_CHECKER_MAYO_DOMAIN) || !hasDomain) {
			return LinkCheckerHttpClient.getMayoBasicContext(link);
		} else {
			return LinkCheckerHttpClient.defaultContext;
		}
	}

	public static void initWordPressContext() {
		LinkCheckerHttpClient.wordPressContext = HttpClientContext.create();
		/*
		 * BasicCookieStore cookieStore = new BasicCookieStore();
		 * HttpClientContext context = HttpClientContext.create();
		 * context.setCookieStore(cookieStore); try { String loginUrl =
		 * IntranetContext.WP_URL; if (!loginUrl.endsWith("/")) loginUrl += "/";
		 * loginUrl += "wp-login.php"; HttpUriRequest login =
		 * RequestBuilder.post() .setUri(new URI(loginUrl)) .addParameter("log",
		 * IntranetContext.WP_ADMIN_NAME) .addParameter("pwd",
		 * IntranetContext.WP_ADMIN_PASSWORD) .addParameter("wp-submit",
		 * "Log In") .addParameter("testcookie", "1") .build();
		 * CloseableHttpResponse response =
		 * LinkCheckerClient.httpClient.execute(login, context);
		 * 
		 * LinkCheckerClient.wordPressContext = HttpClientContext.create();
		 * BasicCookieStore wpCookieStore = new BasicCookieStore(); for (Cookie
		 * cookie : cookieStore.getCookies()) { if
		 * (cookie.getName().startsWith("wordpress_logged_in_")) {
		 * wpCookieStore.addCookie(cookie); break; } }
		 * LinkCheckerClient.wordPressContext.setCookieStore(wpCookieStore);
		 * response.close(); } catch (Exception e) {
		 * IntranetContext.LOG.error("Error loading WordPress context:",e); }
		 */
	}

	private static void initWordPressAuthenticatedContext() {
		BasicCookieStore cookieStore = new BasicCookieStore();
		HttpClientContext context = HttpClientContext.create();
		context.setCookieStore(cookieStore);
		try {
			HttpUriRequest login = RequestBuilder.post().setUri(new URI(LinkCheckerContext.WP_LOGIN))
					.addParameter("log", LinkCheckerContext.WP_ADMIN_NAME)
					.addParameter("pwd", LinkCheckerContext.WP_ADMIN_PASSWORD).addParameter("wp-submit", "Log In")
					.addParameter("testcookie", "1").build();
			CloseableHttpResponse response = LinkCheckerHttpClient.httpClient.execute(login, context);

			LinkCheckerHttpClient.wordPressAuthenticatedContext = HttpClientContext.create();
			BasicCookieStore wpCookieStore = new BasicCookieStore();
			for (Cookie cookie : cookieStore.getCookies()) {
				if (cookie.getName().startsWith("wordpress_logged_in_")) {
					wpCookieStore.addCookie(cookie);
					break;
				}
			}
			LinkCheckerHttpClient.wordPressAuthenticatedContext.setCookieStore(wpCookieStore);
			response.close();
		} catch (Exception e) {
			LinkCheckerContext.LOG.error("Error loading WordPress context:", e);
		}

		/*
		 * String login_url = IntranetContext.WP_URL; if
		 * (!login_url.endsWith("/")) login_url += "/"; login_url +=
		 * "wp-login.php"; List<NameValuePair> formparams = new
		 * ArrayList<NameValuePair>(); formparams.add(new
		 * BasicNameValuePair("log", IntranetContext.WP_ADMIN_NAME));
		 * formparams.add(new BasicNameValuePair("pwd",
		 * IntranetContext.WP_ADMIN_PASSWORD)); formparams.add(new
		 * BasicNameValuePair("wp-submit", "Log In")); formparams.add(new
		 * BasicNameValuePair("testcookie", "1")); UrlEncodedFormEntity entity =
		 * new UrlEncodedFormEntity(formparams, Consts.UTF_8); HttpPost post =
		 * new HttpPost(login_url); post.setEntity(entity);
		 * post.setHeader("Accept", IntranetContext.LINK_CHECKER_ACCEPTS);
		 * post.setHeader("Accept-Language",
		 * IntranetContext.LINK_CHECKER_ACCEPTS_LANGUAGE);
		 * 
		 * CookieStore cookieStore = new BasicCookieStore();
		 * cookieStore.addCookie(new
		 * BasicClientCookie("wordpress_test_cookie","WP+Cookie+check"));
		 * LinkCheckerClient.wordPressAuthenticatedContext.setCookieStore(
		 * cookieStore);
		 * 
		 * try { CloseableHttpClient client = LinkCheckerClient.httpClient;
		 * HttpResponse response = client.execute(post,
		 * LinkCheckerClient.wordPressAuthenticatedContext); } catch (Exception
		 * e) { IntranetContext.LOG.error("Error loading WordPress context:",e);
		 * }
		 */
	}

	// TODO: Test to see if URL needs to be passed in
	private static HttpClientContext getMayoBasicContext(URI uri) throws Exception {
		// create the execution context
		HttpClientContext context = HttpClientContext.create();

		// if we have passed in credentials, we need to handle authentication
		// add NT authentication data
		CredentialsProvider credsProvider = new BasicCredentialsProvider();
		credsProvider.setCredentials(AuthScope.ANY, LinkCheckerContext.NT_CREDENTIALS);

		// Create AuthCache instance
		AuthCache authCache = new BasicAuthCache();
		// Generate NTLM scheme object and add it to the local auth cache
		NTLMScheme ntlmScheme = new NTLMScheme();
		authCache.put(new HttpHost(uri.getHost()), ntlmScheme);

		// Add AuthCache to the execution context
		context.setCredentialsProvider(credsProvider);
		context.setAuthCache(authCache);

		return context;
	}

	// create our incredibly liberal trust manager
	private static void initTrustManager() {
		LinkCheckerHttpClient.trustManager = new X509TrustManager() {
			public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
			}

			public X509Certificate[] getAcceptedIssuers() {
				return null;
			}

			public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
			}
		};
	};

	// create the default request config
	private static void initDefaultRequestConfig() {
		LinkCheckerHttpClient.requestConfig = RequestConfig.custom()
				.setSocketTimeout(LinkCheckerContext.LINK_CHECKER_SOCKET_TIMEOUT)
				.setConnectTimeout(LinkCheckerContext.LINK_CHECKER_CONNECTION_TIMEOUT)
				.setConnectionRequestTimeout(LinkCheckerContext.LINK_CHECKER_CONNECTION_TIMEOUT)
				.setCircularRedirectsAllowed(true).build();
	}
}
