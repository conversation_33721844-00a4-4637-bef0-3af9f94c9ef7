package edu.mayo.web.linkchecker;

import java.io.Serializable;
import java.util.HashMap;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;

@XmlRootElement
public class LinkCheckerLinkStatus implements Serializable {

	private static final long serialVersionUID = -5166196917100738926L;

	private int statusCode = LinkCheckerContext.STATUS_OK;
	private long scanTime = 0;
	private int warningId = -1;

	@XmlElement
	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	@XmlTransient
	public long getScanTime() {
		return scanTime;
	}

	public void setScanTime(long scanTime) {
		this.scanTime = scanTime;
	}

	@XmlElement
	public int getWarningId() {
		return warningId;
	}

	public void setWarningId(int warningId) {
		this.warningId = warningId;
	}

	@XmlTransient
	public boolean isFlagged() {
		if (statusCode != LinkCheckerContext.STATUS_OK)
			return true;
		return false;
	}

	@XmlElement
	public String getStatusMessage() {
		return LinkCheckerLinkStatus.getStatusMessage(this.getStatusCode(), this.getWarningId());
	}

	@XmlElement
	public String getHttpStatusCode() {
		return LinkCheckerLinkStatus.getHttpStatusCode(this.statusCode);
	}

	// start of static messages
	private static String getStatusMessage(int statusCode, int warningId) {
		if (warningId > 0) {
			return getWarningStatusMessage(warningId);
		} else {
			String msg = LinkCheckerContext.STATUS_MESSAGES.get(statusCode);
			if (msg != null)
				return msg;
			else
				return "";
		}
	}

	private static HashMap<Integer, String> warningMessageHash = new HashMap<Integer, String>();
	static {
		try {
			for (LinkCheckerWarning warning : LinkCheckerDatabaseService.getLinkCheckWarnings(true)) {
				LinkCheckerLinkStatus.warningMessageHash.put(new Integer(warning.getId()), warning.getMessage());
			}

		} catch (Exception report) {
			LinkCheckerContext.LOG.error("Error loading status messages from DB: ", report);
		}
	}

	private static String getWarningStatusMessage(int id) {
		try {
			if (LinkCheckerLinkStatus.warningMessageHash.size() == 0) {
				synchronized (LinkCheckerLinkStatus.warningMessageHash) {
					for (LinkCheckerWarning warning : LinkCheckerDatabaseService.getLinkCheckWarnings(true)) {
						LinkCheckerLinkStatus.warningMessageHash.put(new Integer(warning.getId()),
								warning.getMessage());
					}
				}
			}

			return LinkCheckerLinkStatus.warningMessageHash.get(new Integer(id));
		} catch (Exception report) {
			LinkCheckerContext.LOG.error("unable to get warning status message: id=" + id, report);
		}
		return "";
	}

	private static String getHttpStatusCode(int linkCheckStatusCode) {
		switch (linkCheckStatusCode) {
		case LinkCheckerContext.STATUS_OK:
			return "200";
		case LinkCheckerContext.STATUS_404:
			return "404";
		case LinkCheckerContext.STATUS_401:
			return "401";
		case LinkCheckerContext.STATUS_400:
			return "400";
		case LinkCheckerContext.STATUS_NOT_FOUND:
			return "404";
		case LinkCheckerContext.STATUS_OLD_SITE:
			return "200";
		default:
			return "N/A";
		}
	}

}
