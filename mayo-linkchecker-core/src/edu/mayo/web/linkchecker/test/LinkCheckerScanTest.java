package edu.mayo.web.linkchecker.test;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.ArrayList;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerService;

public class LinkCheckerScanTest {

    public static void main(final String[] args) throws Exception {
    	
        try {
        	LinkCheckerRequest lcRequest = new LinkCheckerRequest();
        	//Create link check request
//        	String siteUrl = args[0];
//        	String siteUrl = "https://intranet.mayo.edu/charlie/transplant-center-specialty-council/";
//        	String siteUrl = "https://intranet.mayo.edu/charlie/he-cs-editorial-knowledge-base/";
//        	String siteUrl = "https://intranet.mayo.edu/charlie/inside-mayo-clinic-research/";
//           	String siteUrl = "https://intranet.mayo.edu/charlie/revenue-cycle-office-team/";
           	String siteUrl = "https://intranet.mayo.edu/charlie/jmp-software/";
           	
        	
			lcRequest.setSiteUrl(LinkCheckerContext.formatSiteUrl(new URI(siteUrl)));
		
        	if (args.length > 1) {       		
        		String oldSiteString = args[1];
        		if (oldSiteString.trim().length() > 1) {
	    	    	//create our list of conditionals to flag for warnings
	    			ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
	        		String[] oldSiteBits = oldSiteString.split("\\|");
	    	    	LinkCheckerConditional tempConditional = null;
	    	    	for (int i = 0;i<oldSiteBits.length;i++) {
	    	    		try {
	    		   			tempConditional =  new LinkCheckerConditional(LinkCheckerContext.formatSiteUrl(new URI(oldSiteBits[i])), LinkCheckerContext.STATUS_OLD_SITE);
	    					conditionals.add(tempConditional);
	    	    		} catch (Exception report) {
	    					LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site URL.", report);
	    	    		}
	    			}
	    			lcRequest.setConditionals(conditionals);
        		}
        	}		

       		
        	if (args.length > 2) {
        		String exceptionString = args[2];
    			ArrayList<URI> exceptionSites = new ArrayList<URI>();
            	
        		String[] exceptionSiteBits = exceptionString.split("\\|");
    			for (int i = 0;i<exceptionSiteBits.length;i++) {
    				try {
    					exceptionSites.add(LinkCheckerContext.formatLinkUrl(new URI(exceptionSiteBits[i])));
    				} catch (Exception report) {
    					LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site Exception URL.", report);
    				}
    			}
    			lcRequest.setConditionalExcludes(exceptionSites);
        	}
        	
        	if (args.length > 3) {
        		String emailString = args[3];
        		if (emailString != null && emailString.trim().length() > 0)
        			lcRequest.setEmailAddress(emailString.trim());
        	}
    		
   			lcRequest.setIncludePrivate(true);
   			lcRequest.setIncludePosts(true);
   			lcRequest.setPriority(0);		
    		lcRequest.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
   			
    		int requestId = LinkCheckerDatabaseService.writeRequest(lcRequest);
    		LinkCheckerService.processRequest(requestId);
			
        } catch (MalformedURLException e) {
            LinkCheckerContext.LOG.error("Error Running LinkCheckerServiceTest: " + e.getMessage(), e);
        }         	
    }
 
}
