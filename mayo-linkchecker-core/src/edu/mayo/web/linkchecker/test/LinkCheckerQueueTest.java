package edu.mayo.web.linkchecker.test;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.ArrayList;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerService;

public class LinkCheckerQueueTest {

    public static void main(final String[] args) throws Exception {
    	
        try {
        	LinkCheckerRequest lcRequest = new LinkCheckerRequest();
        	//Create link check request
        	String siteUrl = args[0];
			lcRequest.setSiteUrl(LinkCheckerContext.formatSiteUrl(new URI(siteUrl)));
		
        	if (args.length > 1) {       		
        		String oldSiteString = args[1];
        		if (oldSiteString.trim().length() > 1) {
	    	    	//create our list of conditionals to flag for warnings
	    			ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
	        		String[] oldSiteBits = oldSiteString.split("\\|");
	    	    	LinkCheckerConditional tempConditional = null;
	    	    	for (int i = 0;i<oldSiteBits.length;i++) {
	    	    		try {
	    		   			tempConditional =  new LinkCheckerConditional(LinkCheckerContext.formatSiteUrl(new URI(oldSiteBits[i])), LinkCheckerContext.STATUS_OLD_SITE);
	    					conditionals.add(tempConditional);
	    	    		} catch (Exception report) {
	    					LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site URL.", report);
	    	    		}
	    			}
	    			lcRequest.setConditionals(conditionals);
        		}
        	}		

       		
        	if (args.length > 2) {
        		String exceptionString = args[2];
    			ArrayList<URI> exceptionSites = new ArrayList<URI>();
            	
        		String[] exceptionSiteBits = exceptionString.split("\\|");
    			for (int i = 0;i<exceptionSiteBits.length;i++) {
    				try {
    					exceptionSites.add(LinkCheckerContext.formatLinkUrl(new URI(exceptionSiteBits[i])));
    				} catch (Exception report) {
    					LinkCheckerContext.LOG.error("Error in LinkCheckerRequest URL.  Ignoring Old Site Exception URL.", report);
    				}
    			}
    			lcRequest.setConditionalExcludes(exceptionSites);
        	}
        	
        	if (args.length > 3) {
        		String emailString = args[3];
        		if (emailString != null && emailString.trim().length() > 0)
        			lcRequest.setEmailAddress(emailString.trim());
        	}
    		
   			lcRequest.setIncludePrivate(true);
   			lcRequest.setIncludePosts(true);
   			lcRequest.setPriority(0);		
    		lcRequest.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
   			
    		//add the request to the request queue
    		//also gets the generated id
    		lcRequest.setId(LinkCheckerDatabaseService.writeRequest(lcRequest));
    		
    		//run any requests in the queue, including the one we just dropped in there
    		while (LinkCheckerDatabaseService.getNextRequest() != null) {
    			LinkCheckerService.runNextRequest();
    		}
			
        } catch (MalformedURLException e) {
            LinkCheckerContext.LOG.error("Error Running LinkCheckerServiceTest: " + e.getMessage(), e);
        }         	
    }
 
}
