package edu.mayo.web.linkchecker.test;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerService;
import edu.mayo.web.linkchecker.service.LinkCheckerWordPressService;
import edu.mayo.web.linkchecker.servlet.LinkCheckerReportServlet;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

public class LinkCheckerReportTest {

    public static void main(final String[] args) throws Exception {
    	
        try {
//        	String siteUrl = args[0];
//        	String siteUrl = "https://intranet.mayo.edu/charlie/transplant-center-specialty-council/";
        	//String siteUrl = "https://intranet.mayo.edu/charlie/he-cs-editorial-knowledge-base/";
//        	String siteUrl = "https://intranet.mayo.edu/charlie/inside-mayo-clinic-research/";
        	String siteUrl = "https://intranet.mayo.edu/charlie/labor-law-posters/";
        	//Not finding pages
//        	String siteUrl = "http://intranet.mayo.edu/charlie/hepatobiliary-pancreas-surgery-rst";
        	//not scanning
//        	String siteUrl = "http://intranet.mayo.edu/charlie/committees-arz/";
        	URI siteUri = LinkCheckerContext.formatSiteUrl(new URI(siteUrl));
/*			
Long startTime = (new Date()).getTime();        	
           	LinkCheckerSite lcSite = LinkCheckerDatabaseService.getLinkCheckSite(siteUri);
Long stopTime = (new Date()).getTime();           	
           	System.out.println(lcSite.toJSON());
System.out.println("time: "+(stopTime - startTime));
*/           	

//			Long startTime = (new Date()).getTime();        	
			String report = LinkCheckerService.getLinkCheckReport(siteUri.toString());
//			Long stopTime = (new Date()).getTime();           	
			System.out.println(report);
//			Long time = stopTime - startTime;
//			System.out.println("time: "+time);           	


/*
Long startTime = (new Date()).getTime();        	
	LinkCheckerSite lcSite = LinkCheckerDatabaseService.getLinkCheckSite(siteUri);
Long stopTime = (new Date()).getTime();           	
	System.out.println(lcSite.toJSON());
System.out.println("time: "+(stopTime - startTime));           	
*/
        } catch (MalformedURLException e) {
            LinkCheckerContext.LOG.error("Error Running LinkCheckerReportTest: " + e.getMessage(), e);
        }         	
    }
 
}
