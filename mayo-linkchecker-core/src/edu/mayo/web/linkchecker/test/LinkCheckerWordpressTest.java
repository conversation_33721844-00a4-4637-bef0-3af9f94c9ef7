package edu.mayo.web.linkchecker.test;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerPage;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerService;
import edu.mayo.web.linkchecker.service.LinkCheckerWordPressService;
import edu.mayo.web.linkchecker.servlet.LinkCheckerReportServlet;
import edu.mayo.web.linkchecker.wordpress.WordpressDatabaseUtils;
import edu.mayo.web.linkchecker.wordpress.WordpressPost;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

public class LinkCheckerWordpressTest {

    public static void main(final String[] args) throws Exception {
    	
        try {
        	List<WordpressSite> wpSites = WordpressDatabaseUtils.getAllWordpressSites();
        	
        	for (WordpressSite site : wpSites) {
        		System.out.println(site.getSiteUrl());
        	}
       	
        } catch (MalformedURLException e) {
            LinkCheckerContext.LOG.error("Error Running LinkCheckerWordpressTest: " + e.getMessage(), e);
        }         	
    }
 
}
