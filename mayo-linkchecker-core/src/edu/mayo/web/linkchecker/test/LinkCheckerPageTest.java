package edu.mayo.web.linkchecker.test;

import java.net.MalformedURLException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerPage;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;
import edu.mayo.web.linkchecker.service.LinkCheckerService;
import edu.mayo.web.linkchecker.service.LinkCheckerWordPressService;
import edu.mayo.web.linkchecker.servlet.LinkCheckerReportServlet;
import edu.mayo.web.linkchecker.wordpress.WordpressDatabaseUtils;
import edu.mayo.web.linkchecker.wordpress.WordpressPost;
import edu.mayo.web.linkchecker.wordpress.WordpressSite;

public class LinkCheckerPageTest {

    public static void main(final String[] args) throws Exception {
    	
        try {
        	
        	int pageId = 710403;
        	int siteId = 1994;
        	String siteUrl = "https://intranet.mayo.edu/charlie/continuous-accreditation-licensure-compliance/";
        	String pageUrl = "https://intranet.mayo.edu/charlie/continuous-accreditation-licensure-compliance/home/";
        	
        	LinkCheckerRequest lcRequest = new LinkCheckerRequest();
        	lcRequest.setSiteUrl(LinkCheckerContext.formatSiteUrl(new URI(siteUrl)));
        	lcRequest.setIncludePrivate(true);
   			lcRequest.setIncludePosts(true);
   			lcRequest.setPriority(0);		
    		lcRequest.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
        	
        	LinkCheckerPage page = LinkCheckerDatabaseService.getLinkCheckPage(pageId);
        	WordpressSite wpSite = WordpressDatabaseUtils.getWordpressSite(siteUrl);
        	List<WordpressPost> wpPosts = WordpressDatabaseUtils.getPosts(wpSite, false);
        	WordpressPost wpPost = null;
        	
        	for (WordpressPost tempPost : wpPosts) {
        		if (tempPost.getUrl().equalsIgnoreCase(pageUrl)) {
        			page.setWpPost(tempPost);
        			break;
        		}
        	}
        	
        	
        	LinkCheckerPage.checkPage(page, siteId, lcRequest);
        	
        } catch (MalformedURLException e) {
            LinkCheckerContext.LOG.error("Error Running LinkCheckerReportTest: " + e.getMessage(), e);
        }         	
    }
 
}
