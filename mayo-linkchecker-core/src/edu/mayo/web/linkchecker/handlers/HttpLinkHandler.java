package edu.mayo.web.linkchecker.handlers;

import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;

import javax.net.ssl.SSLException;

import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;

import edu.mayo.web.linkchecker.LinkCheckerHttpClient;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerLinkStatus;

public class HttpLinkHandler {

	public static boolean matches(URI link) {
		if (link.getScheme().equalsIgnoreCase("http") || link.getScheme().equalsIgnoreCase("https"))
			return true;
		return false;
	}

	public static LinkCheckerLinkStatus evaluate(URI link) {
		LinkCheckerLinkStatus status = checkLink(link, false);
		// because they are cached for later use, we want to re-check 404 errors
		// to verify
		if (status.getStatusCode() == LinkCheckerContext.STATUS_404
				|| status.getStatusCode() == LinkCheckerContext.STATUS_DEFAULT_ERROR) {
			status = checkLink(link, true);
			if (status.getStatusCode() == LinkCheckerContext.STATUS_404
					|| status.getStatusCode() == LinkCheckerContext.STATUS_DEFAULT_ERROR) {
				status = checkLink(link, true);
			}
		}
		return status;
	}

	private static LinkCheckerLinkStatus checkLink(URI link, boolean isRetry) {
System.out.println(link.toString());		
		LinkCheckerLinkStatus status = new LinkCheckerLinkStatus();
		CloseableHttpResponse response = null;
		HttpGet get = null;
		long startTime = 0;

		try {
			if (isRetry)
				Thread.sleep(LinkCheckerContext.LINK_CHECKER_RETRY_WAIT);

			startTime = System.currentTimeMillis();
			// deep check by downloading complete page
			get = new HttpGet(link);
			get.setConfig(LinkCheckerHttpClient.requestConfig);
			get.addHeader(HttpHeaders.ACCEPT, LinkCheckerContext.LINK_CHECKER_ACCEPTS);
			get.addHeader(HttpHeaders.ACCEPT_LANGUAGE, LinkCheckerContext.LINK_CHECKER_ACCEPTS_LANGUAGE);
			response = LinkCheckerHttpClient.getHttpClient().execute(get, LinkCheckerHttpClient.getHttpClientContext(link));

			status.setScanTime((System.currentTimeMillis() - startTime));
			StatusLine line = response.getStatusLine();
			if (line.getStatusCode() == HttpStatus.SC_NOT_FOUND) {
				status.setStatusCode(LinkCheckerContext.STATUS_404);
			} else if (line.getStatusCode() == HttpStatus.SC_UNAUTHORIZED) {
				status.setStatusCode(LinkCheckerContext.STATUS_401);
			} else if (line.getStatusCode() == HttpStatus.SC_BAD_REQUEST) {
				status.setStatusCode(LinkCheckerContext.STATUS_400);
			} else if (line.getStatusCode() != HttpStatus.SC_OK) {
				status.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
			} else {
				status.setStatusCode(LinkCheckerContext.STATUS_OK);
			}
		} catch (UnknownHostException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_404);
			// LinkCheckerContext.LOG.debug("Error checking link
			// (UnknownHostException): "+link);
		} catch (SocketTimeoutException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_404);
			// LinkCheckerContext.LOG.debug("Error checking link
			// (SocketTimeoutException: "+link);
		} catch (SSLException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_401);
			// LinkCheckerContext.LOG.error("Error checking link (SSLException):
			// "+link);
		} catch (IllegalArgumentException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_BAD_FORMAT);
			// LinkCheckerContext.LOG.error("Error in link format:
			// (IllegalArgumentException) "+link);
		} catch (URISyntaxException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_BAD_FORMAT);
			// LinkCheckerContext.LOG.error("Error in link format
			// (URISyntaxException): "+link);
		} catch (ClientProtocolException e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			status.setStatusCode(LinkCheckerContext.STATUS_BAD_FORMAT);
			// LinkCheckerContext.LOG.error("Error in link format
			// (ClientProtocolException: "+link);
		} catch (Exception e) {
			status.setScanTime((System.currentTimeMillis() - startTime));
			// LinkCheckerContext.LOG.error("Unknown Error checking link
			// ("+e.getMessage()+"): "+link);
			status.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (Exception report) {
					LinkCheckerContext.LOG.error("Error closing HTTP connection", report);
				}
			}
		}
		// LinkCheckerContext.LOG.debug("checked http link:
		// "+status.getStatusCode());

		return status;
	}

}
