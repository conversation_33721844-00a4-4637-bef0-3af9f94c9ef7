package edu.mayo.web.linkchecker.handlers;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerLinkStatus;
import edu.mayo.web.linkchecker.LinkCheckerWarning;

public class WarningsLinkHandler {

	public static boolean matches(URI link) {
		List<LinkCheckerWarning> warnings = new ArrayList<LinkCheckerWarning>();
		synchronized (LinkCheckerContext.LINK_CHECKER_WARNINGS) {
			warnings = LinkCheckerContext.LINK_CHECKER_WARNINGS;
		}
		for (LinkCheckerWarning warning : warnings) {
			if (warning.evaluate(link) == true)
				return true;
		}
		return false;
	}

	public static LinkCheckerLinkStatus evaluate(URI link) {
		List<LinkCheckerWarning> warnings = new ArrayList<LinkCheckerWarning>();
		synchronized (LinkCheckerContext.LINK_CHECKER_WARNINGS) {
			warnings = LinkCheckerContext.LINK_CHECKER_WARNINGS;
		}
		LinkCheckerLinkStatus status = new LinkCheckerLinkStatus();
		for (LinkCheckerWarning warning : warnings) {
			if (warning.evaluate(link) == true) {
				status.setStatusCode(LinkCheckerContext.STATUS_WARNING_FLAGGED);
				status.setWarningId(warning.getId());
				return status;
			}
		}

		return status;
	}

}
