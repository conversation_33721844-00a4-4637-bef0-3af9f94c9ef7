package edu.mayo.web.linkchecker.handlers;

import java.io.File;
import java.net.URI;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerLinkStatus;

public class BasicFileLinkHandler {

	public static boolean matches(URI link) {
		if (link.getScheme().equalsIgnoreCase("file"))
			return true;
		return false;
	}

	public static LinkCheckerLinkStatus evaluate(URI link) {
		LinkCheckerLinkStatus status = new LinkCheckerLinkStatus();
		long startTime = System.currentTimeMillis();
		try {
			// get the file path from the passed-in URL
			String filePath = link.toString().replace("%20", " ").replace("/", "\\");
			filePath = filePath.substring(7, filePath.length());

			if (filePath.startsWith(LinkCheckerContext.LINK_CHECKER_MFAD_STRING))
				filePath = "\\\\" + filePath;

			File f = new File(filePath);
			if (f.exists() && f.canRead()) {
				status.setStatusCode(LinkCheckerContext.STATUS_OK);
			} else {
				status.setStatusCode(LinkCheckerContext.STATUS_NOT_FOUND);
			}
		} catch (Exception e) {
			status.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
			// LinkCheckerContext.LOG.error("File check failed for
			// "+link.toString(),e);

		}
		// LinkCheckerContext.LOG.debug("checked file link:
		// "+status.getStatusCode());
		status.setScanTime(System.currentTimeMillis() - startTime);
		return status;
	}

}
