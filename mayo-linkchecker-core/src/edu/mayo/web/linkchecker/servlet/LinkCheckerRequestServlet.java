package edu.mayo.web.linkchecker.servlet;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import edu.mayo.web.linkchecker.LinkCheckerConditional;
import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerRequest;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;

/**
 * Servlet implementation class LinkCheckerRequestServlet
 */
public class LinkCheckerRequestServlet extends HttpServlet {

	private static final long serialVersionUID = 8268186633257835027L;
	private static final String ACTION_ADD = "linkchecker_request_add";
	private static final String ACTION_GET = "linkchecker_request_get";

	public void init(ServletConfig config) throws ServletException {
		super.init(config);
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		LinkCheckerRequestResponse lcResponse = new LinkCheckerRequestResponse();
		String action = request.getParameter("action");
		if (action == null || action.trim().length() == 0)
			action = LinkCheckerRequestServlet.ACTION_GET;
		if (action.equalsIgnoreCase(LinkCheckerRequestServlet.ACTION_ADD)) { // process
																				// an
																				// ADD
																				// request
			try {
				// get passed in parameters
				String sitesString = request.getParameter("sites");
				String emailStr = request.getParameter("email");
				String includePrivateStr = request.getParameter("include-private");
				String includePostsStr = request.getParameter("include-posts");
				String priorityStr = request.getParameter("priority");

				// create our array of old sites - we flag these links with a
				// warning
				String oldSiteString = request.getParameter("old-sites");
				ArrayList<LinkCheckerConditional> conditionals = new ArrayList<LinkCheckerConditional>();
				if (oldSiteString != null && oldSiteString.length() > 0) {
					// create our list of conditionals to flag for warnings
					String[] oldSiteBits = oldSiteString.split("\\|");
					LinkCheckerConditional tempConditional = null;
					for (int i = 0; i < oldSiteBits.length; i++) {
						try {
							tempConditional = new LinkCheckerConditional(
									LinkCheckerContext.formatSiteUrl(new URI(oldSiteBits[i])),
									LinkCheckerContext.STATUS_OLD_SITE);
							conditionals.add(tempConditional);
						} catch (Exception report) {
							LinkCheckerContext.LOG.debug("Error creating old site url: ", report);
						}
					}
				}

				String exceptionString = request.getParameter("exception-sites");
				ArrayList<URI> exceptionSites = new ArrayList<URI>();
				if (exceptionString != null && exceptionString.length() > 0) {
					String[] exceptionSiteBits = exceptionString.split("\\|");
					for (int i = 0; i < exceptionSiteBits.length; i++) {
						try {
							exceptionSites.add(LinkCheckerContext.formatLinkUrl(new URI(exceptionSiteBits[i])));
						} catch (Exception report) {
							LinkCheckerContext.LOG.debug("Error creating exception url: ", report);
						}
					}
				}

				// add the requests to the request queue
				LinkCheckerRequest lcRequest = null;
				String[] siteBits = sitesString.split("\\|");
				URI siteUri;
				for (String siteUrl : siteBits) {
					if (siteUrl.length() > 5) {
						try {
							siteUri = new URI(siteUrl);
							lcRequest = LinkCheckerDatabaseService.getQueuedRequest(siteUri);
							if (lcRequest != null) {
								if (emailStr != null && emailStr.trim().length() > 0)
									lcRequest.setEmailAddress(emailStr.trim());
								if (includePrivateStr != null && includePrivateStr.equalsIgnoreCase("true"))
									lcRequest.setIncludePrivate(true);
								if (includePostsStr != null && includePostsStr.equalsIgnoreCase("true"))
									lcRequest.setIncludePosts(true);
								if (priorityStr != null && priorityStr.trim().length() > 0)
									lcRequest.setPriority(Integer.valueOf(priorityStr));
								lcRequest.setConditionals(conditionals);
								lcRequest.setConditionalExcludes(exceptionSites);
							} else {
								lcRequest = new LinkCheckerRequest();
								lcRequest.setStatusCode(LinkCheckerContext.STATUS_NOT_STARTED);
								lcRequest.setSiteUrl(new URI(siteUrl));
								if (emailStr != null && emailStr.trim().length() > 0)
									lcRequest.setEmailAddress(emailStr.trim());
								if (includePrivateStr != null && includePrivateStr.equalsIgnoreCase("true"))
									lcRequest.setIncludePrivate(true);
								if (includePostsStr != null && includePostsStr.equalsIgnoreCase("true"))
									lcRequest.setIncludePosts(true);
								if (priorityStr != null && priorityStr.trim().length() > 0)
									lcRequest.setPriority(Integer.valueOf(priorityStr));
								lcRequest.setConditionals(conditionals);
								lcRequest.setConditionalExcludes(exceptionSites);
							}
							// write the request
							lcRequest.setId(LinkCheckerDatabaseService.writeRequest(lcRequest));
							List<LinkCheckerRequest> reqList = lcResponse.getRequestList();
							reqList.add(lcRequest);
							lcResponse.setRequestList(reqList);
						} catch (Exception report) {
							LinkCheckerContext.LOG.error("Error queueing link checker request: " + siteUrl, report);
						}
					}
				}
				lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_OK);
				lcResponse.setResponseMessage("OK");
			} catch (Exception report) {
				lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
				lcResponse.setResponseMessage("Error queueing link checker request: " + report.getLocalizedMessage());
				LinkCheckerContext.LOG.error("Error queueing link checker request: ", report);
			}
		} else if (action.equalsIgnoreCase(LinkCheckerRequestServlet.ACTION_GET)) { // process
																					// a
																					// GET
																					// request
			try {
				String siteUrl = request.getParameter("site-url");
				LinkCheckerRequest lcRequest = LinkCheckerDatabaseService.getQueuedRequest(new URI(siteUrl));
				if (lcRequest != null) {
					List<LinkCheckerRequest> reqList = lcResponse.getRequestList();
					reqList.add(lcRequest);
					lcResponse.setRequestList(reqList);
					lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_OK);
					lcResponse.setResponseMessage("OK");
				} else {
					lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_NOT_FOUND);
					lcResponse.setResponseMessage("No Request Found for URL: " + siteUrl);
				}
			} catch (Exception report) {
				lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
				lcResponse.setResponseMessage("Error returning link checker request: " + report.getLocalizedMessage());
				LinkCheckerContext.LOG.error("Error returning link checker request: ", report);
			}
		} else {
			lcResponse.setSubmissionStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
			lcResponse.setResponseMessage("Error with server call: NO VALID ACTION DEFINED");
		}

		// return the results of the request to the requestor
		try {
			response.getWriter().write(lcResponse.toJSON().toString());
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
		} catch (Exception e) {
			throw new IOException(e);
		}

	}

}
