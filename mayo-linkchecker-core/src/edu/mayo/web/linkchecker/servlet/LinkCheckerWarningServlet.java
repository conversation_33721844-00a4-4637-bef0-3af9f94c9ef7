package edu.mayo.web.linkchecker.servlet;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerWarning;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;

/**
 * Servlet implementation class LinkCheckerRequestServlet
 */
public class LinkCheckerWarningServlet extends HttpServlet {

	private static final long serialVersionUID = 8268186633257835027L;
	public static final String ACTION_GET = "linkchecker_warning_get";
	public static final String ACTION_UPDATE = "linkchecker_warning_add";

	public void init(ServletConfig config) throws ServletException {
		super.init(config);
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		try {
			LinkCheckerWarningResponse lcWarningResp = new LinkCheckerWarningResponse();

			// get passed in parameters
			String action = request.getParameter("action");
			if (action == null || action.length() == 0)
				action = LinkCheckerWarningServlet.ACTION_GET;

			try {
				if (action.equalsIgnoreCase(LinkCheckerWarningServlet.ACTION_UPDATE)) {
					// process warning update/add
					LinkCheckerWarning warning = new LinkCheckerWarning();
					warning.setId(Integer.valueOf(request.getParameter("id")));
					warning.setActive(Boolean.valueOf(request.getParameter("active")));
					warning.setExactMatch(Boolean.valueOf(request.getParameter("exactMatch")));
					warning.setMessage(request.getParameter("message"));
					warning.setPattern(new URI(request.getParameter("pattern")));

					// write the updated/new linkchecker warning
					warning.setId(LinkCheckerDatabaseService.writeLinkCheckWarning(warning));

					// get and return updated list of warnings
					ArrayList<LinkCheckerWarning> warnings = new ArrayList<LinkCheckerWarning>();
					warnings.add(warning);
					lcWarningResp.setWarnings(warnings);
					lcWarningResp.setStatusCode(LinkCheckerContext.STATUS_OK);
					lcWarningResp.setResponseMessage("OK");
				} else {
					// get and return list of warnings
					lcWarningResp.setWarnings(LinkCheckerDatabaseService.getLinkCheckWarnings(true));
					lcWarningResp.setStatusCode(LinkCheckerContext.STATUS_OK);
					lcWarningResp.setResponseMessage("OK");
				}
			} catch (Exception e) {
				lcWarningResp.setStatusCode(LinkCheckerContext.STATUS_DEFAULT_ERROR);
				lcWarningResp.setResponseMessage("Error with link checker warning request: " + e.getLocalizedMessage());
				LinkCheckerContext.LOG.error("Error with link checker warning request: ", e);
			}

			response.getWriter().write(lcWarningResp.toJSON().toString());
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
		} catch (Exception e) {
			throw new IOException(e);
		}
	}

}
