package edu.mayo.web.linkchecker.servlet;

import java.io.IOException;
import java.net.URI;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerSite;
import edu.mayo.web.linkchecker.service.LinkCheckerDatabaseService;

/**
 * Servlet implementation class LinkCheckerRequestServlet
 */
public class LinkCheckerReportServlet extends HttpServlet {

	private static final long serialVersionUID = 8268186633257835027L;
	private static final String FORMAT_XML = "xml";
	private static final String FORMAT_JSON = "json";
	private static String MSG_NO_SITE = "Error retrieving report: No 'site-url' parameter was specified.";
	private static String MSG_NO_DATA = "Error retrieving report: No link checker reports have been run for this site yet";
	private static String MSG_ERROR = "Error retrieving report: ";

	public void init(ServletConfig config) throws ServletException {
		// TODO Auto-generated method stub
		super.init(config);
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String responseStr = LinkCheckerReportServlet.MSG_NO_DATA;

		String format = "html";
		String formatParam = request.getParameter("format");
		if (formatParam != null && formatParam.length() >= 0)
			format = formatParam.trim();

		try {
			if (request.getParameter("site-url") != null && request.getParameter("site-url").length() > 0) {
				LinkCheckerSite lcSite = LinkCheckerDatabaseService
						.getLinkCheckSite(LinkCheckerContext.formatSiteUrl(new URI(request.getParameter("site-url"))));
				if (lcSite != null) {
					// if we have a site report, we publish it
					if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_JSON)) {
						responseStr = lcSite.toJSON().toString();
					} else if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_XML))
						responseStr = lcSite.toXML();
					else
						responseStr = lcSite.toHTML();
				} else {
					// else, we return the 'no-data' response
					if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_XML))
						responseStr = "<lcSite><errorMsg>" + LinkCheckerReportServlet.MSG_NO_DATA
								+ "</errorMsg></lcSite>";
					else if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_JSON))
						responseStr = "{lcSite:{errorMsg:" + LinkCheckerReportServlet.MSG_NO_DATA + "}}";
					else
						responseStr = "<html><body><span>" + LinkCheckerReportServlet.MSG_NO_DATA
								+ "</span></body></html>";
				}
			} else {
				// return error - no site-url
				if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_XML))
					responseStr = "<lcSite><errorMsg>" + LinkCheckerReportServlet.MSG_NO_SITE + "</errorMsg></lcSite>";
				else if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_JSON))
					responseStr = "{lcSite:{errorMsg:" + LinkCheckerReportServlet.MSG_NO_SITE + "}}";
				else
					responseStr = "<html><body><span>" + LinkCheckerReportServlet.MSG_NO_SITE + "</span></body></html>";
			}
		} catch (Exception e) {
			// return general error
			LinkCheckerContext.LOG.error(LinkCheckerReportServlet.MSG_ERROR, e);
			String errorStr = LinkCheckerReportServlet.MSG_ERROR + e.getMessage();
			if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_XML))
				responseStr = "<lcSite><errorMsg>" + errorStr + "</errorMsg></lcSite>";
			else if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_JSON))
				responseStr = "{lcSite:{errorMsg:" + errorStr + "}}";
			else
				responseStr = "<html><body><span>" + errorStr + "</span></body></html>";
		}

		// write the response
		if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_JSON))
			response.setContentType("application/json");
		else if (format.equalsIgnoreCase(LinkCheckerReportServlet.FORMAT_XML))
			response.setContentType("application/xml");
		else
			response.setContentType("text/html");
		response.setCharacterEncoding("utf-8");
		response.getWriter().write(responseStr);
	}

}
