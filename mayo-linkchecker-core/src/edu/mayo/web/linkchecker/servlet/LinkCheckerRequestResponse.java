package edu.mayo.web.linkchecker.servlet;

import java.io.Serializable;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.json.JSONObject;
import org.json.XML;

import edu.mayo.web.linkchecker.LinkCheckerContext;
import edu.mayo.web.linkchecker.LinkCheckerRequest;

@XmlRootElement
public class LinkCheckerRequestResponse implements Serializable {

	private static final long serialVersionUID = 7100712992880510240L;
	private String responseMessage = "";
	private int submissionStatusCode = LinkCheckerContext.STATUS_DEFAULT_ERROR;
	private List<LinkCheckerRequest> requestList = new ArrayList<LinkCheckerRequest>();

	public LinkCheckerRequestResponse() {
	}

	@XmlElement
	public String getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}

	@XmlElement
	public int getSubmissionStatusCode() {
		return submissionStatusCode;
	}

	public void setSubmissionStatusCode(int submissionStatusCode) {
		this.submissionStatusCode = submissionStatusCode;
	}

	@XmlElement
	public List<LinkCheckerRequest> getRequestList() {
		return requestList;
	}

	public void setRequestList(List<LinkCheckerRequest> requestList) {
		this.requestList = requestList;
	}

	public JSONObject toJSON() throws Exception {
		return XML.toJSONObject(this.toXML());
	}

	public String toXML() throws Exception {
		JAXBContext jaxbContext = JAXBContext.newInstance(LinkCheckerRequestResponse.class);
		Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

		// output pretty printed
		jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
		jaxbMarshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
		StringWriter output = new StringWriter();
		jaxbMarshaller.marshal(this, output);
		return output.toString();
	}
}
