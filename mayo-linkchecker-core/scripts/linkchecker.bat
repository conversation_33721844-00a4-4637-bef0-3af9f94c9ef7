set SERVICE=Link<PERSON>he<PERSON>
set HOME_DIR=c:\linkchecker
set CERT_DIR="C:\Program Files\AdoptOpenJDK\jre-8.0.222.10-hotspot\lib\security\cacerts"
set LOCK_FILE=linkchecker.lock

echo "%SERVICE% called"
cd %HOME_DIR%

if EXIST %LOCK_FILE% (
	echo "%SERVICE% service running, everything is fine"
) else (
	echo "%SERVICE% is not running"
	echo. 2>%LOCK_FILE%
	java -cp "%HOME_DIR%\\lib\\*" edu.mayo.web.linkchecker.service.LinkCheckerService %CERT_DIR%
	del %LOCK_FILE%
)