<h2>Link Checker Request Form</h2>
<form id="request-form" method="post">
  <strong>Sites</strong>
  <div id="site-div">
    <input id="sites" type="hidden" name="sites"/>
    <input class="site" type="text"><br/>
  </div>
  <button id="add-site" type="button" class="button button-small">
    Add Another Site
  </button><br/>
  <br/>
  <strong>Old Sites</strong>
  <div id="old-site-div">
    <input id="old-sites" type="hidden" name="old-sites"/>
    <input class="old-site" type="text"><br/>
  </div>
  <button id="add-old-site" type="button" class="button button-small">
    Add Another Old Site
  </button><br/>
  <br/>
  <strong>Exceptions for Old Sites</strong>
  <div id="exception-site-div">
    <input id="exception-sites" type="hidden" name="exception-sites"/>
    <input class="exception-site" type="text"><br/>
  </div>
  <button id="add-exception-site" type="button" class="button button-small">
    Add Another Old Site Exception
  </button><br/>
  <br/>
  <strong>Include posts:</strong>
  <select id="include-posts" name="include-posts">
    <option value="false">No</option>
    <option value="true">Yes</option>
  </select><br/>
  <strong>Include unpublished pages/posts:</strong>
  <select id="include-private" name="include-private">
    <option value="false">No</option>
    <option value="true">Yes</option>
  </select><br/>
  <br/>
  <input id="priority" type="hidden" name="priority" value="2"/>
  <input type="submit" id="submit" class="button button-primary" data-nonce="<?php echo $nonce ?>"/>
</form>
