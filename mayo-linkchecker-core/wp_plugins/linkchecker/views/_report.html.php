<ul class="subsubsub">
  <?php foreach ($report->link_types as $type => $count): ?>
    <li>
      <a href="<?php echo add_query_arg('link_type', $type) ?>"
	 class="<?php echo ($link_type == $type) ? 'current' : '' ?>">
	<?php echo $type ?> <span class="count">(<?php echo $count ?>)</span>
      </a>
    </li>
  <?php endforeach ?>
</ul>
<div class="clear"></div>

<?php foreach ($report->pages as $page): ?>
  <div class="postbox">
    <div class="hndle ui-sortable-handle page-header">
      <a target="_blank" href='<?php echo $page->url ?>'>
        <?php echo $page->path ?>
      </a>
    </div>
    <div class="inside">
      <table class="widefat fixed">
        <tbody>
          <?php foreach ($page->links as $link): ?>
            <tr>
              <td>
                <?php echo $link->caption ?><br>
		<a href="<?php echo $link->href ?>" target="_blank">
	          <?php echo $link->href ?>
		</a>
              </td>
              <td>
		<?php echo $link->status->statusMessage ?><br>
		(<?php echo $link->status->httpStatusCode ?>)
	      </td>
            </tr>
          <?php endforeach ?>
        </tbody>
      </table>
    </div>
  </div>
<?php endforeach ?>
