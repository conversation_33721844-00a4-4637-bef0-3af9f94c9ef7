<strong>Next Scan</strong><br>
<div class="request-info">
  <div id="request-status">
    <?php if ($queued): ?>
      A link checker scan has been requested by a site
      administrator.<br>
      The requestor will receive a notification email
      when the scan has completed.<br>
      The updated results will be displayed on this
      report.
    <?php else: ?>
      <button id="request-button" class="button button-primary" data-nonce="<?php echo $nonce ?>">
	Request an updated scan
      </button><br>
      The next scan will be run on the automated scan
      schedule (usually run weekly).<br>
      <strong>Can't wait?</strong>
      Press the 'Request an updated scan' button to
      request a link checker scan.
    <?php endif ?>
  </div>
</div>
