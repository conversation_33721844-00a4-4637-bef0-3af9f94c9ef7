<div class="wrap">
  <?php if (!$production): ?>
    <div class="notice notice-warning">
      <p>
	<b>WARNING: You are in a testing environment.</b><br/>
	<b>Changes made to the warnings below will also take effect on production.</b>
      </p>
    </div>
  <?php endif ?>
  <h2>Link Checker Warnings</h2>
  Manage the URL patterns flagged by the Link Checker.<br>  The links
  matching the URL Patterns defined here will be marked as "flagged
  links" in the Link Checker Reports.
  <div class="metabox-holder">
    <div class="summarybox">
      <div class="meta-box-sortables ui-sortable">
        <?php if ($json->statusCode == 9): ?>
          <table class="widefat">
            <thead>
              <td>URL Pattern</td>
              <td>Message</td>
              <td>Exact Match</td>
              <td>Active</td>
              <td></td>
            </thead>
            <tbody>
              <tr class="alternate">
                <td class="pattern"><textarea></textarea></td>
                <td class="message"><textarea></textarea></td>
                <td><input type="checkbox" class="is-exact"/></td>
                <td>
                  <input type="checkbox" class="is-active" checked="checked"/>
                </td>
                <td><div class="id">-1</div>
                  <div class="edit-values"></div>
                  <button class="edit-warning button button-small">Edit</button>
                  <button class="reset-warning button button-small">
                    Cancel
                  </button>
                  <button class="update-warning button button-small" data-nonce="<?php echo $nonce ?>">
                    Update
                  </button>
                  <button class="add-warning button button-primary button-small" data-nonce="<?php echo $nonce ?>">
                    Add
                  </button>
                </td>
              </tr>
              <?php foreach ($json->warnings as $warning): ?>
                <tr>
                  <td class="pattern"><?php echo $warning->pattern ?></td>
                  <td class="message"><?php echo $warning->message ?></td>
                  <td>
                    <?php if ($warning->exactMatch == true): ?>
                      <input type="checkbox" class="is-exact" checked="checked"/>
                    <?php else: ?>
                      <input type="checkbox" class="is-exact"/>
                    <?php endif ?>
                  </td>
                  <td>
                    <?php if ($warning->active == true): ?>
                      <input type="checkbox" class="is-active" checked="checked"/>
                    <?php else: ?>
                      <input type="checkbox" class="is-active"/>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="id"><?php echo $warning->id ?></div>
                    <div class="edit-values"></div>
                    <button class="edit-warning button button-small">
                      Edit
                    </button>
                    <button class="reset-warning button button-small">
                      Cancel
                    </button>
                    <button class="update-warning button button-small" data-nonce="<?php echo $nonce ?>">
                      Update
                    </button>
                  </td>
                </tr>
              <?php endforeach ?>
            </tbody>
          </table>
        <?php else: ?>
          There was an error loading the warning table
        <?php endif ?>
      </div>
    </div>
  </div>
</div>
