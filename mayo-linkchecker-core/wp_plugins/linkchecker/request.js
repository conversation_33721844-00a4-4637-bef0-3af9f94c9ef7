jQuery(function () {
    //reset to default values
    resetValues();

    jQuery(".site").blur(validateWPURL);
    jQuery("#add-site").click(function (event) {
        var input = jQuery("<input/>", {
            "class": "site",
            type: "text",
            blur: validateWPURL
        });
        jQuery("#site-div").append(input).append("<br/>");
    });

    jQuery(".old-site").blur(validateURL);
    jQuery("#add-old-site").click(function (event) {
        var input = jQuery("<input/>", {
            "class": "old-site",
            type: "text",
            blur: validateURL
        });
        jQuery("#old-site-div").append(input).append("<br/>");
    });

    jQuery(".exception-site").blur(validateURL);
    jQuery("#add-exception-site").click(function (event) {
        var input = jQuery("<input/>", {
            "class": "exception-site",
            type: "text",
            blur: validateURL
        });
        jQuery("#exception-site-div").append(input).append("<br/>");
    });

    jQuery("#submit").click(submit);

});

function resetValues() {
    jQuery("input").val("");
    jQuery("#include-posts").val("false");
    jQuery("#include-private").val("false");
    jQuery("#priority").val("2");
    jQuery("#sites").val("");
    jQuery(".site").not(':first').remove();
    jQuery("#site-div br").not(':first').remove();
    jQuery("#old-sites").val("");
    jQuery(".old-site").not(':first').remove();
    jQuery("#old-site-div br").not(':first').remove();
    jQuery("#exception-sites").val("");
    jQuery(".exception-site").not(':first').remove();
    jQuery("#exception-site-div br").not(':first').remove();
    jQuery("#submit").val("Submit Request").prop('disabled', false);
}

function validateWPURL(e) {
    var url = jQuery(e.target).val().trim();
    if (url.length > 0) {
        if (/^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i.test(url)) {
            if ((url.toLowerCase().indexOf("http://intranet.mayo.edu/charlie/") != 0) && (url.toLowerCase().indexOf("http://mayoweb.mayo.edu/") != 0)) {
                alert("Error: URL must start with 'http://intranet.mayo.edu/charlie/' or 'http://mayoweb.mayo.edu/'");
                jQuery(e.target).val("");
                jQuery(e.target).focus();
                return false;
            }
        } else {
            alert("Please enter a valid URL");
            jQuery(e.target).val("");
            jQuery(e.target).focus();
            return false;
        }
    }
}

function validateURL(e) {
    var url = jQuery(e.target).val().trim();
    if (url.length > 0) {
        if (/^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i.test(url) == false) {
            alert("Please enter a valid URL");
            jQuery(e.target).val("");
            jQuery(e.target).focus();
            return false;
        }
    }
}

function submit(event) {
    if (jQuery(".site").val() != "") {
        jQuery("#sites").val("");
        jQuery(".site").each(function (index, item) {
            jQuery("#sites").val(jQuery("#sites").val() + jQuery(item).val().trim() + "|");
        });
        jQuery("#old-sites").val("");
        jQuery(".old-site").each(function (index, item) {
            jQuery("#old-sites").val(jQuery("#old-sites").val() + jQuery(item).val().trim() + "|");
        });

        jQuery("#exception-sites").val("");
        jQuery(".exception-site").each(function (index, item) {
            jQuery("#exception-sites").val(jQuery("#exception-sites").val() + jQuery(item).val().trim() + "|");
        });

        var data = {
            'action': 'linkchecker_request_add',
            'sites': jQuery("#sites").val(),
            'old-sites': jQuery("#old-sites").val(),
            'exception-sites': jQuery("#exception-sites").val(),
            'include-private': jQuery("#include-private").val(),
            'include-posts': jQuery("#include-posts").val(),
            'email': jQuery("#email").val(),
            'priority': 2,
	    '_ajax_nonce': jQuery(this).attr('data-nonce')
        };

        jQuery('#submit').prop('disabled', true);
        // since 2.8 ajaxurl is always defined in the admin header and points to admin-ajax.php	
        jQuery.post(ajaxurl, data, function (response) {
            var responseJSON = jQuery.parseJSON(response);
            if (responseJSON != -1 && responseJSON.linkCheckerRequestResponse.submissionStatusCode == 9) { //9=OK
                alert('Request was successfully submitted.');
		resetValues();
            } else {
		text = 'There was an error submitting your request: \n';
		if (responseJSON == "-1") {
		    text += "Unable to reach LinkChecker service";
		} else {
		    text += responseJSON.linkCheckerRequestResponse.responseMessage;
		}
		alert(text);
		jQuery('#submit').prop('disabled', 0);
            }
        });
    } else {
        alert("Please enter a site URL to check");
    }

    return false;
}
