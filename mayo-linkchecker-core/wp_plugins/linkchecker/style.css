/* report page styles */

.page-header a {
    line-height: 1.4em;
    font-size: 14px;
    margin: 6px;
    font-weight: 600;
}

.left-col {
    float: left;
    padding-right: 30px;
}

textarea {
    overflow: visible;
    color: #555;
    font-size: 13px;
    line-height: 1.5em;
    vertical-align: top;
    padding: 8px 10px;
    word-wrap: break-word;
    font-family: inherit;
    font-weight: inherit;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
    border-spacing: 0;
    width: 100%;
    height: 100%;
}

td {
    min-height: 20px;
    height: 100%;
}

.page-header {
    display: block;
    font-weight: 600;
    color: #222;
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}


/* request page styles */

.site {
    width: 650px;
}

.old-site {
    width: 650px;
}

.exception-site {
    width: 650px;
}

.email {
    width: 650px;
}
