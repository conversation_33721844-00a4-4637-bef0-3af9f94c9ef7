function stripe_tables() {
    jQuery('table tr:odd').addClass('alt');
}

jQuery(function () {
    stripe_tables();
    jQuery('#request-button').click(submitRequest);
});

function submitRequest(event) {
    jQuery('#request-button').prop('disabled', true);

    var data = {
	'action': 'linkchecker_request_add',
	'_ajax_nonce': jQuery(this).attr('data-nonce')
    }

    jQuery.post(ajaxurl, data, function (response) {
        var responseJSON = jQuery.parseJSON(response);
        if (responseJSON != '-1' && responseJSON.linkCheckerRequestResponse.submissionStatusCode == 9) { //9=OK
            var requestMsg1 = "Your link checker scan request was successfully submitted.";
            var requestMsg2 = "You will receive the report in an email when the scan has completed.";
            var requestMsg3 = "The updated results will be displayed on this report.";
            jQuery('#request-button').prop('disabled', true).text("Request has been submitted.");
            jQuery("#request-status").html(requestMsg1 + "<br>" + requestMsg2 + "<br>" + requestMsg3);
            alert(requestMsg1 + "\n" + requestMsg2 + "\n" + requestMsg3);
        } else {
            var msg1 = "There was an error submitting your request.";
            var msg2 = "Please contact the WordPress team for help correcting the error.";
	    if (responseJSON != '-1') {
		var msg3 = "Error Message:\n" + responseJSON.linkCheckerRequestResponse.responseMessage;
	    }
            jQuery('#request-button').prop('disabled', false);
            jQuery("#request-status").html(msg1 + "<br>" + msg2 + "<br>" + msg3);
            alert(msg1 + "\n" + msg2 + "\n" + msg3);
        }
    });

    return false;
}
