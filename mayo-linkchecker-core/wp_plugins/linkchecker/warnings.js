function stripe_tables() {
    jQuery('table tr:odd').addClass('alt');
}

jQuery(function () {
    stripe_tables();

    //set the initial field and button states
    jQuery(".edit-warning:first").hide();
    jQuery(".reset-warning").hide();
    jQuery(".update-warning").hide();
    jQuery(".add-warning").not(":first").hide();
    jQuery(".id").hide();
    jQuery(".edit-values").hide();
    jQuery(".is-exact").not(":first").prop("disabled", true);
    jQuery(".is-active").not(":first").prop("disabled", true);

    //add handlers to enable editing
    jQuery(".edit-warning").click(editWarning);
    jQuery(".reset-warning").click(resetWarning);
    jQuery(".update-warning").click(updateWarning);
    jQuery(".add-warning").click(addWarning);
});

function addWarning(e) {
    //submit new warning to web service
    var row = jQuery(e.target).parent().parent();
    //first disable buttons to reduce chance of double-click error
    row.find(".add-warning").prop("disabled", true);

    //get the URL value and validate it
    var url = row.find(".pattern textarea").val().trim();
    if (url.length == 0) {
        alert("Please enter a value in the URL field");
        row.find(".pattern textarea").focus();
        row.find(".add-warning").prop("disabled", false);
        return false;
    } else if (/^(file|http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i.test(url) == false) {
        alert("Please enter a valid URL in the URL field");
        row.find(".pattern textarea").focus();
        row.find(".add-warning").prop("disabled", false);
        return false;
    }

    //get the message value and validate
    var message = row.find(".message textarea").val().trim();
    if (message.length == 0) {
        alert("Please enter a value in the Message field");
        row.find(".message textarea").focus();
        row.find(".add-warning").prop("disabled", false);
        return false;
    } else if (message.toLowerCase().indexOf("<script") > -1) {
        alert("You cannot insert <script> tags in the Message field");
        row.find(".message textarea").focus();
        row.find(".add-warning").prop("disabled", false);
        return false;
    }

    //submit new data to web service
    var data = {
        'action': 'linkchecker_warning_add',
        'id': row.find(".id").text(),
        'pattern': url,
        'message': message,
        'exactMatch': row.find(".is-exact").prop("checked"),
        'active': row.find(".is-active").prop("checked"),
	'_ajax_nonce': jQuery(this).attr('data-nonce')
    };

    // since 2.8 ajaxurl is always defined in the admin header and points to admin-ajax.php	
    jQuery.post(ajaxurl, data, function (response) {
        var responseJSON = jQuery.parseJSON(response);
        var row = jQuery(e.target).parent().parent();
        //if success, turn to non-editable row and insert new "ADD" row
        if (responseJSON != -1 && responseJSON.linkCheckerWarningResponse.statusCode == 9) { //9=OK
            var newAddRow = row.clone();
            if (newAddRow.hasClass("alternate"))
                newAddRow.removeClass("alternate");
            else
                newAddRow.addClass("alternate");
            newAddRow.find(".pattern").html("<textarea />");
            newAddRow.find(".message").html("<textarea />");
            newAddRow.find(".is-exact").prop("checked", false).prop("disabled", false);
            newAddRow.find(".is-active").prop("checked", true).prop("disabled", false);
            newAddRow.find(".id").html("-1");
            newAddRow.find(".edit-warning").html("");
            newAddRow.find(".add-warning").prop("disabled", false);
            //add the newAddRow before the top row
            row.before(newAddRow);

            //CHANGE old add row to NON_EDITABLE
            row.find(".pattern").html(responseJSON.linkCheckerWarningResponse.warnings.pattern);
            row.find(".message").html(responseJSON.linkCheckerWarningResponse.warnings.message);
            row.find(".is-exact").prop("checked", responseJSON.linkCheckerWarningResponse.warnings.exactMatch).prop("disabled", true);
            row.find(".is-active").prop("checked", responseJSON.linkCheckerWarningResponse.warnings.active).prop("disabled", true);
            row.find(".id").html(responseJSON.linkCheckerWarningResponse.warnings.id);
            //reset button layout
            row.find(".edit-warning").show();
            row.find(".reset-warning").hide();
            row.find(".update-warning").hide();
            row.find(".add-warning").hide();
        } else { //if fail, show warning
	    text = 'There was an error updating this Link Checker warning: \n';
	    if (responseJSON == -1) {
		text += "Unable to reach LinkChecker service";
	    } else {
		text += responseJSON.linkCheckerRequestResponse.responseMessage;
	    }
	    alert(text);
            row.find(".add-warning").prop("disabled", false);
        }
    });

}

function updateWarning(e) {
    var row = jQuery(e.target).parent().parent();
    //first disable buttons to reduce chance of double-click error
    row.find(".reset-warning").prop("disabled", true);
    row.find(".update-warning").prop("disabled", true);

    //get the URL value and validate it
    var url = row.find(".pattern textarea").val().trim();
    if (url.length == 0) {
        alert("Please enter a value in the URL field");
        row.find(".pattern textarea").focus();
        row.find(".reset-warning").prop("disabled", false);
        row.find(".update-warning").prop("disabled", false);
        return false;
    } else if (/^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i.test(url) == false) {
        alert("Please enter a valid URL in the URL field");
        row.find(".pattern textarea").focus();
        row.find(".reset-warning").prop("disabled", false);
        row.find(".update-warning").prop("disabled", false);
        return false;
    }

    //get the message value and validate
    var message = row.find(".message textarea").val().trim();
    if (message.length == 0) {
        alert("Please enter a value in the Message field");
        row.find(".message textarea").focus();
        row.find(".reset-warning").prop("disabled", false);
        row.find(".update-warning").prop("disabled", false);
        return false;
    } else if (message.toLowerCase().indexOf("<script") > -1) {
        alert("You cannot insert <script> tags in the Message field");
        row.find(".message textarea").focus();
        row.find(".reset-warning").prop("disabled", false);
        row.find(".update-warning").prop("disabled", false);
        return false;
    }

    //submit new data to web service
    var data = {
        'action': 'linkchecker_warning_add',
        'id': row.find(".id").text(),
        'pattern': url,
        'message': message,
        'exactMatch': row.find(".is-exact").prop("checked"),
        'active': row.find(".is-active").prop("checked"),
	'_ajax_nonce': jQuery(this).attr('data-nonce')
    };

    // since 2.8 ajaxurl is always defined in the admin header and points to admin-ajax.php	
    jQuery.post(ajaxurl, data, function (response) {
        var responseJSON = jQuery.parseJSON(response);
        var row = jQuery(e.target).parent().parent();
        //if success, return to non-editable state
        if (responseJSON != -1 && responseJSON.linkCheckerWarningResponse.statusCode == 9) { //9=OK
            row.find(".pattern").html(responseJSON.linkCheckerWarningResponse.warnings.pattern);
            row.find(".message").html(responseJSON.linkCheckerWarningResponse.warnings.message);
            row.find(".is-exact").prop("checked", responseJSON.linkCheckerWarningResponse.warnings.exactMatch).prop("disabled", true);
            row.find(".is-active").prop("checked", responseJSON.linkCheckerWarningResponse.warnings.active).prop("disabled", true);
            row.find(".id").html(responseJSON.linkCheckerWarningResponse.warnings.id);
            //reset button layout
            row.find(".edit-warning").show();
            row.find(".reset-warning").hide();
            row.find(".update-warning").hide();
        } else { //if fail, show warning
	    text = 'There was an error updating this Link Checker warning: \n';
	    if (responseJSON == -1) {
		text += "Unable to reach LinkChecker service";
	    } else {
		text += responseJSON.linkCheckerRequestResponse.responseMessage;
	    }
	    alert(text);
        }
        row.find(".reset-warning").prop("disabled", false);
        row.find(".update-warning").prop("disabled", false);
    });

}

function resetWarning(e) {
    //restore current values and non-editable UI state
    var row = jQuery(e.target).parent().parent();
    //get the current values from hidden div and put in array
    var oldVals = row.find(".edit-values").text().split("|");
    row.find(".edit-values").text("");
    row.find(".pattern").html(oldVals[0]);
    row.find(".message").html(oldVals[1]);
    if (oldVals[2] == "false")
        row.find(".is-exact").prop("checked", false).prop("disabled", true);
    else
        row.find(".is-exact").prop("checked", true).prop("disabled", true);
    if (oldVals[3] == "false")
        row.find(".is-active").prop("checked", false).prop("disabled", true);
    else
        row.find(".is-active").prop("checked", true).prop("disabled", true);

    //reset button layout
    row.find(".edit-warning").show();
    row.find(".reset-warning").hide();
    row.find(".update-warning").hide();
}

function editWarning(e) {
    var row = jQuery(e.target).parent().parent();
    //store current values as a string in a hidden div for later retrieval
    var editVals = row.find(".pattern").text() + "|" + row.find(".message").text() + "|" + row.find(".is-exact").prop("checked") + "|" + row.find(".is-active").prop("checked");
    row.find(".edit-values").text(editVals);
    //make fields editable
    row.find(".pattern").html("<textarea>" + row.find(".pattern").text() + "</textarea>");
    row.find(".message").html("<textarea>" + row.find(".message").text() + "</textarea>");
    row.find(".is-exact").prop("disabled", false);
    row.find(".is-active").prop("disabled", false);
    //set 'edit' button layout
    row.find(".edit-warning").hide();
    row.find(".reset-warning").show();
    row.find(".update-warning").show();
}
