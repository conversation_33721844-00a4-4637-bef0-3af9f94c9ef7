<?php
/*
Plugin Name: <PERSON> Link Checker
Description: Integrates Mayo Link Checker into admin panel
Version: 1.0.0
Author: <PERSON> and <PERSON>
Author URI: http://intranet.mayo.edu/charlie/
*/

namespace Mayo\LinkChecker;

define('BASE_API_URL', 'http://roeneo905a.mayo.edu:8080/mayo-linkchecker');
define('WARNING_API_URL', BASE_API_URL.'/warning');
define('REQUEST_API_URL', BASE_API_URL.'/request');
define('REPORT_API_URL', BASE_API_URL.'/report');

// LinkChecker API response link->status->statusCode constants
define('OLD_SITE_LINK', 6);
define('OK', 9);

add_action('admin_menu', '\Mayo\LinkChecker\admin_menu');
add_action('network_admin_menu', '\Mayo\LinkChecker\network_admin_menu');
add_action('wp_ajax_linkchecker_request_add',
           '\Mayo\LinkChecker\request_add_callback');
add_action('wp_ajax_linkchecker_warning_add',
           '\Mayo\LinkChecker\warning_add_callback');

function check_permissions($capability)
{
    if (!current_user_can($capability)) {
        echo 'You do not have sufficient permissions to access this page.';
        wp_die();
    }
}

function warning_add_callback()
{
    check_permissions('manage_network_plugins');

    check_ajax_referer('mayo_linkchecker_warning_request');

    $response = wp_remote_post(WARNING_API_URL, array('body' => $_POST));
    echo wp_remote_retrieve_body($response);

    wp_die(); // required to return proper JSON response
}

function request_add_callback()
{
    check_permissions('manage_options');

    check_ajax_referer('mayo_linkchecker_request_report');

    $defaults = array(
        'sites' => production_blog_secure_url(),
        'old-sites' => '',
        'exception-sites' => '',
        'include-private' => '',
        'include-posts' => '',
        'priority' => 1
    );

    $body = array_merge($defaults, $_POST);
    $body['email'] = wp_get_current_user()->user_email;

    $response = wp_remote_post(REQUEST_API_URL, array('body' => $body));
    echo wp_remote_retrieve_body($response);

    wp_die(); // required to return proper JSON response
}

function admin_menu()
{
    add_menu_page('Link Checker Report',
                  'Link Checker Report',
                  'manage_options',
                  'mayo_linkchecker_admin_menu',
                  '\Mayo\LinkChecker\write_report');
}

function network_admin_menu()
{
    add_menu_page('Link Checker Request',
                  'Link Checker Request',
                  'manage_options',
                  'mayo_linkchecker_network_admin_menu',
                  '\Mayo\LinkChecker\write_request');
    add_submenu_page('mayo_linkchecker_network_admin_menu',
                     'Link Checker Warnings',
                     'Link Checker Warnings',
                     'manage_network_plugins',
                     'mayo_linkchecker_warning_menu',
                     '\Mayo\LinkChecker\write_warning');
}

function include_style()
{
    wp_register_style('mayo_linkchecker_style',
                      plugins_url('/style.css', __FILE__));
    wp_enqueue_style('mayo_linkchecker_style');
}

function include_script($script)
{
    wp_register_script("mayo_linkchecker_{$script}_script",
                       plugins_url("/{$script}.js", __FILE__),
                       array('jquery', 'jquery-ui-core', 'utils'),
                       '1.0');
    wp_enqueue_script("mayo_linkchecker_{$script}_script");
}

function write_warning()
{
    check_permissions('manage_network_plugins');

    include_script('warnings');
    include_style();

    $response = wp_remote_get(WARNING_API_URL);
    $json = wp_remote_retrieve_body($response);
    $json = json_decode($json);
    $json = $json->linkCheckerWarningResponse;

    $nonce = wp_create_nonce('mayo_linkchecker_warning_request');
    $production = is_production();

    include 'views/warnings.html.php';
}

function write_report()
{
    check_permissions('manage_options');

    include_script('report');
    include_style();

    if (array_key_exists("link_type", $_GET)) {
        $link_type = $_GET["link_type"];
    } else {
        $link_type = "Not Found";
    }

    $blog_url = production_blog_secure_url();
    $production = is_production();

    $url = REPORT_API_URL.'?format=JSON&site-url='.urlencode($blog_url);
    $response = wp_remote_get($url, array('timeout' => 10));
    $report = generate_report(wp_remote_retrieve_body($response), $link_type);

    $url = REQUEST_API_URL.'?format=JSON&site-url='.urlencode($blog_url);
    $response = wp_remote_get($url, array('timeout' => 10));
    $json = json_decode(wp_remote_retrieve_body($response));
    $queued = $json->linkCheckerRequestResponse->submissionStatusCode === OK;

    $nonce = wp_create_nonce('mayo_linkchecker_request_report');

    include 'views/report.html.php';
}

function write_request()
{
    check_permissions('manage_network_plugins');

    include_script('request');
    include_style();

    $nonce = wp_create_nonce('mayo_linkchecker_request_report');

    include 'views/request.html.php';
}

function generate_report($json, $link_type)
{
    $json = json_decode($json);

    if ($json === null) {
        return false;
    }

    $json = $json->linkCheckerSite;

    // normalize LinkChecker response
    // alter json->pages to always return an array
    if (!is_array($json->pages)) {
        $json->pages = array($json->pages);
    }

    // alter page->links to always return an array
    foreach ($json->pages as $page) {
        if (!is_array($page->links)) {
            $page->links = array($page->links);
        }
    }

    // get the counts we need for display
    $json->oldSiteLinkCount = 0;
    $json->totalFlaggedLinkCount = 0;
    foreach ($json->pages as $page) {
        foreach ($page->links as $link) {
            if ($link->status->statusCode == OLD_SITE_LINK) {
                ++$json->oldSiteLinkCount;
            } else {
                ++$json->totalFlaggedLinkCount;
            }
        }
    }

    // derive path for every page
    foreach ($json->pages as $page) {
        $url = parse_url($page->url);
        $page->path = $url['path'];
        if (isset($url['query'])) {
            $page->path .= '?'.$url['query'];
        }
    }

    // find untitled link and give them a caption
    foreach ($json->pages as $page) {
        foreach ($page->links as $link) {
            if (empty($link->caption)) {
                $link->caption = '(untitled link)';
            }
        }
    }

    // sort links by types
    $link_types = array("Not Found", "Not Checked", "Old", "Other");

    // initialize link type counter
    $json->link_types = array();
    foreach ($link_types as $type) {
        $json->link_types[$type] = 0;
    }

    $status_to_type = array(
        1 => "Not Found",
        2 => "Not Found",
        3 => "Not Checked",
        4 => "Not Checked",
        5 => "Not Found",
        6 => "Old",
        7 => "Other",
        8 => "Not Found",
        9 => "Other",
        10 => "Other",
        11 => "Other",
        12 => "Other",
        13 => "Other"
    );

    $status_messages_array = array();    
    foreach ($json->$statusMessages->$entry as $tempEntry) {
        $status_messages_array[$entry->$key] = $entry->$value;
    }
    $json->$status_message_array = $status_messages_array;

    $warning_messages_array = array();    
    foreach ($json->$warningMessages->$entry as $tempEntry) {
        $warning_messages_array[$entry->$key] = $entry->$value;
    }
    $json->$warning_message_array = $warning_messages_array;
    
    foreach ($json->pages as $page) {
        $page->links_by_type = array();

        foreach ($page->links as $link) {
            $type = $status_to_type[$link->status->statusCode];
            $link->status->type = $type;
            $json->link_types[$type]++;

            if ($link_type != $type) {
                $key = array_search($link, $page->links);
                unset($page->links[$key]);
            }
        }
    }

    // remove empty link types
    foreach ($json->link_types as $type => $count) {
        if ($count === 0) {
            unset($json->link_types[$type]);
        }
    }

    // remove pages without links
    foreach ($json->pages as $page) {
        if (count($page->links) === 0) {
            $key = array_search($page, $json->pages);
            unset($json->pages[$key]);
        }
    }

    return $json;
}

function build_url(array $parts)
{
    return (
        isset($parts['scheme']) ? "{$parts['scheme']}:" : '') .
        ((isset($parts['user']) || isset($parts['host'])) ? '//' : '') .
        (isset($parts['user']) ? "{$parts['user']}" : '') .
        (isset($parts['pass']) ? ":{$parts['pass']}" : '') .
        (isset($parts['user']) ? '@' : '') .
        (isset($parts['host']) ? "{$parts['host']}" : '') .
        (isset($parts['port']) ? ":{$parts['port']}" : '') .
        (isset($parts['path']) ? "{$parts['path']}" : '') .
        (isset($parts['query']) ? "?{$parts['query']}" : '') .
        (isset($parts['fragment']) ? "#{$parts['fragment']}" : '');
}

function production_blog_url()
{
    $blog_url = parse_url(get_bloginfo('url'));
    $host = $blog_url['host'];
    $blog_url['host'] = 'intranet.mayo.edu';
    $blog_url['path'] = preg_replace("/\/branch[0-9]/", "/charlie", $blog_url['path']);
    $blog_url['scheme'] = 'http';
    $blog_url = build_url($blog_url);
    return $blog_url;
}

function production_blog_secure_url()
{
    $blog_url = parse_url(get_bloginfo('url'));
    $host = $blog_url['host'];
    $blog_url['host'] = 'intranet.mayo.edu';
    $blog_url['path'] = preg_replace("/\/branch[0-9]/", "/charlie", $blog_url['path']);
    $blog_url['scheme'] = 'https';
    $blog_url = build_url($blog_url);
    return $blog_url;
}

function is_production()
{
    if (get_bloginfo('url') === production_blog_url() || get_bloginfo('url') === production_blog_secure_url()) 
        return true;
    return false;
}
