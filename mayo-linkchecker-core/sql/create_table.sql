USE [LINKCHECKER]
GO

/****** Object:  Table [dbo].[wordpress_sites]    Script Date: 02/14/2014 13:53:34 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[link_check_warnings](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[pattern] [varchar](1000) NOT NULL,
	[is_exact_match] [bit] NOT NULL,
	[message] [varchar](1000) NOT NULL,	
	[is_active] [bit] NOT NULL,
 CONSTRAINT [PK_link_check_warnings] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

INSERT into [dbo].[link_check_warnings] VALUES ('http://surveymonkey.com',0,'Because this is a Survey Monkey link, verify that the information being gathered by Survey Monkey is not considered proprietary or in any way patient care-related. If it is, this would be considered a breach of Mayo Clinic data security, requiring the survey be closed and the link removed.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('https://sharepoint.mayo.edu',0,'SharePoint is migrating sites to new URL formats (https://collab.mayo.edu/ and https://project.mayo.edu/). This link points to the older SharePoint URL, please confirm the URL is correct.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/default.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/person/person.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/photos/',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/photoListing/default.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/committee/committee.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/pager/page.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://quarterly.mayo.edu/person/results.cfm',0,'This link is broken.  Please use the <a href="https://collab.mayo.edu/team/Directory/Shared%20Documents/Communication%20Plan/conversion%20document/Conversion%20Document.docx" target="_blank">Quarterly Reference Resource</a> to correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://hree.mayo.edu',0,'This link is broken.  Use the <a href="http://intranet.mayo.edu/charlie/employee-learning" target="_blank">Employee Learning</a> site to locate your training materials and correct the URL.',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://policies.mayo.edu',0,'The link to Policies (UCM) has changed from <a href="http://mayoweb.mayo.edu/policies">http://mayoweb.mayo.edu/policies</a> to: <a href="http://mayocontent.mayo.edu/mcppp-main/index.html" target="_blank">http://mayocontent.mayo.edu/mcppp-main/index.html</a>',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://intranet.mayo.edu/charlie/training/',0,'The WordPress Training site has moved to <a href="http://intranet.mayo.edu/wordpress-training/guide/" target="_blank">http://intranet.mayo.edu/wordpress-training/guide/</a>. ',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://mayoweb.mayo.edu/policies',0,'The link to Policies (UCM) has changed from <a href="http://mayoweb.mayo.edu/policies">http://mayoweb.mayo.edu/policies</a> to: <a href="http://mayocontent.mayo.edu/mcppp-main/index.html" target="_blank">http://mayocontent.mayo.edu/mcppp-main/index.html</a>',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://intranet.mayo.edu/charlie/training/',0,'The WordPress Training site has moved to <a href="http://intranet.mayo.edu/wordpress-training/guide/" target="_blank">http://intranet.mayo.edu/wordpress-training/guide/</a>. ',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://cfprod.mayo.edu/medsys/formulary',0,'This link points to the old Formulary application URL. Please update to the new URL: <a href="http://dotnetprod/medsys/formulary" target="_blank">http://dotnetprod/medsys/formulary</a>',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://mayoweb.mayo.edu/formulary/',0,'This link points to the old Formulary website. Please update to the new URL: <a href="http://intranet.mayo.edu/charlie/formulary/">http://intranet.mayo.edu/charlie/formulary/</a>',1)
INSERT into [dbo].[link_check_warnings] VALUES ('http://mayoweb.mayo.edu/patient-education/',0,'Patient Education moved on April 22, 2015. The new site is at: <a href="http://intranet.mayo.edu/charlie/patient-education-rst/">http://intranet.mayo.edu/charlie/patient-education-rst</a>',1);
INSERT into [dbo].[link_check_warnings] VALUES ('http://patienteducation.mayo.edu/',0,'Patient Education moved on April 22, 2015. The new site is at: <a href="http://intranet.mayo.edu/charlie/patient-education-rst/">http://intranet.mayo.edu/charlie/patient-education-rst</a>',1);
INSERT into [dbo].[link_check_warnings] VALUES ('http://mayoweb.mayo.edu/contribute/',0,'The Contribute training guide moved on October 18, 2015. The new site is at: <a href="http://intranet.mayo.edu/charlie/contribute/">http://intranet.mayo.edu/charlie/contribute/</a>',1);
INSERT into [dbo].[link_check_warnings] VALUES ('http://socrates.mayo.edu/',0,'This link is pointing to a URL on a non-production server (http://socrates.mayo.edu) that should not be linked to.  Update your link to point to the resource on the correct server (http://mayoweb.mayo.edu)',1);

CREATE TABLE [dbo].[link_check_links](
	[page_id] [bigint] NOT NULL,
	[target] [varchar](1000) NOT NULL,
	[tag] [varchar](50) NOT NULL,
	[caption] [varchar](500) NOT NULL,
	[status_code] [int] NOT NULL,
	[warning_id] [int] NOT NULL,
	[href] [varchar](1000) NOT NULL,
) ON [PRIMARY]

GO

CREATE TABLE [dbo].[link_check_pages](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[site_id] [int] NOT NULL,
	[url] [varchar](1000) NOT NULL,
	[status_code] [int] NOT NULL,
	[is_post] [bit] NOT NULL,
	[is_private] [bit] NOT NULL,
 CONSTRAINT [PK_link_check_pages] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

CREATE TABLE [dbo].[link_check_sites](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[url] [varchar](1000) NOT NULL,
	[scan_duration] [bigint] NOT NULL,
	[scan_date] [bigint] NOT NULL,	
	[page_count] [int] NOT NULL,
	[link_count] [int] NOT NULL,
 CONSTRAINT [PK_link_check_sites] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

CREATE TABLE [dbo].[link_check_reports](
	[site_url] [varchar](1000) NOT NULL,
	[report] [VARBINARY](MAX) NOT NULL
);
GO


CREATE TABLE [dbo].[link_check_queue](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[scan_status] [int] NOT NULL,
	[insert_date] [bigint] NOT NULL,
	[site_url] [varchar](1000) NOT NULL,
	[old_site_string] [varchar](5000) NOT NULL,
	[exception_string] [varchar](5000) NOT NULL,
	[include_posts] [bit] NOT NULL,
	[include_private] [bit] NOT NULL,
	[email] [varchar](1000) NOT NULL,
	[priority] [int] NOT NULL,
	[error_msg] [varchar](1000) NOT NULL,
 CONSTRAINT [PK_link_check_queue] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO
