# link checker properties
edu.mayo.web.linkchecker.request.timeout=20000
edu.mayo.web.linkchecker.socket.timeout=10000
edu.mayo.web.linkchecker.connection.timeout=10000
edu.mayo.web.linkchecker.retry.wait=20
edu.mayo.web.linkchecker.connection.pool.size=1

edu.mayo.web.linkchecker.report.header.file=report_header.html
edu.mayo.web.linkchecker.report.wp.script.file=wp_embed_script.html
#edu.mayo.web.linkchecker.report.directory=/Users/<USER>/Projects/Linkchecker/reports
edu.mayo.web.linkchecker.report.directory=D:/linkchecker/reports
edu.mayo.web.linkchecker.key.store.path=/Library/Java/JavaVirtualMachines/jdk-14.0.1.jdk/Contents/Home/lib/security/cacerts
#edu.mayo.web.linkchecker.key.store.path=C:/Program\ Files/AdoptOpenJDK/jre-**********-hotspot/lib/security/cacerts
#edu.mayo.web.linkchecker.key.store.path=C:/Program\ Files/AdoptOpenJDK/jre-**********-hotspot/lib/security/cacerts
#edu.mayo.web.linkchecker.key.store.path=C:\\Program Files\\AdoptOpenJDK\\jre-**********-hotspot\\lib\\security\\cacerts

#edu.mayo.web.linkchecker.supported.encription=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3
edu.mayo.web.linkchecker.supported.encription=TLSv1,TLSv1.1,TLSv1.2
edu.mayo.web.linkchecker.report.max.link.text.length=150
edu.mayo.web.linkchecker.certificates.path=certificates
edu.mayo.web.linkchecker.key.store.password=changeit
edu.mayo.web.linkchecker.result.width=35
edu.mayo.web.linkchecker.user.agent=Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0)
edu.mayo.web.linkchecker.accepts=text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
edu.mayo.web.linkchecker.accepts.language=en-US,en;q=0.5

edu.mayo.web.linkchecker.content.selector=div#wrapper,div#container
edu.mayo.web.linkchecker.link.selector=a[href], [src], link[href]
edu.mayo.web.linkchecker.mayo.domain=mayo.edu
edu.mayo.web.linkchecker.mfad.string=mfad
edu.mayo.web.linkchecker.username=TU06018
edu.mayo.web.linkchecker.password=hod04iF7
edu.mayo.web.linkchecker.use.status.cache=true
edu.mayo.web.linkchecker.report.max.link.text.length=150

## WordPress properties
edu.mayo.web.wordpress.url=https://intranet.mayo.edu/charlie
edu.mayo.web.wordpress.login=https://intranet.mayo.edu/charlie/wp-login.php
edu.mayo.web.wordpress.hostname.short=intranet
edu.mayo.web.wordpress.hostname.full=intranet.mayo.edu
edu.mayo.web.wordpress.admin.url=/wp-admin/
edu.mayo.web.wordpress.login.url=/wp-login.php
edu.mayo.web.wordpress.admin.name=linkchecker
edu.mayo.web.wordpress.admin.password=Ys31q^@zsDBXX8m1$xC$1nW\#
edu.mayo.web.wordpress.db.server=rofwpr011a.mayo.edu
edu.mayo.web.wordpress.db.port=3306
edu.mayo.web.wordpress.db.instance=charlie
edu.mayo.web.wordpress.db.user=charlie
edu.mayo.web.wordpress.db.password=3nRzjEV7v4FUvPMP


#STATUS Messages
edu.mayo.web.linkchecker.status.default.error=This link could not be checked. Please verify that this link points to an existing file or page. If it does not, update the link with the correct URL or remove the link.
edu.mayo.web.linkchecker.status.404=This link points to a file or page that does not exist; it may have been deleted or the link may be misspelled. Please update the link with the correct URL or remove the link.
edu.mayo.web.linkchecker.status.401=This link could not be checked due to site security. Please verify that this link points to an existing file or page. If it does not, update the link with the correct URL or remove the link.
edu.mayo.web.linkchecker.status.400=There was an error checking this link, most likely due to a problem with the URL. Please verify that the URL is correct (including the opening http://, https://, file://, etc.) and adjust accordingly.
edu.mayo.web.linkchecker.status.file.not.found=This link points to a file or folder that does not exist or that could not be checked. Please verify that this link points to an existing file or page. If it does not, update the link with the correct URL or remove the link.
edu.mayo.web.linkchecker.status.old.site=This link points to a web site that has been retired or will soon be retired. Please update the link to point to the new location or remove the link.
edu.mayo.web.linkchecker.status.bad.format=The URL for this link is improperly formatted. Please verify that the URL is correct (including the opening http://, https://, file://, etc.) and adjust accordingly. Local files should be formatted as file://mfad.mfroot.org/folder/folder/file rather than \\\\mfad.mfroot.org\\folder\\folder\\file
edu.mayo.web.linkchecker.status.warning.flagged=A warning has been flagged for this link.
edu.mayo.web.linkchecker.status.ok=OK
edu.mayo.web.linkchecker.status.not.started=Not Started
edu.mayo.web.linkchecker.status.started=Started
edu.mayo.web.linkchecker.status.closed=Closed

## PIPS Inventory DB properties
edu.mayo.web.db.jdbc.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
edu.mayo.web.db.max.url.length=1000
edu.mayo.web.db.max.caption.length=1000
edu.mayo.web.db.max.tag.length=50
edu.mayo.web.db.name=LINKCHECKER

##PROD-level values
#edu.mayo.web.db.connection.string=************************************
edu.mayo.web.db.connection.string=************************************
#edu.mayo.web.db.connection.string=************************************;integratedsecurity=true
#edu.mayo.web.db.instance.name=FDNPRD20
edu.mayo.web.db.instance.name=Default
edu.mayo.web.db.port=1433

##DEV-level values
#edu.mayo.web.db.connection.string=************************************
#edu.mayo.web.db.instance.name=Default
#edu.mayo.web.db.port=1433


edu.mayo.web.smtp.host=smtprelay.mayo.edu
edu.mayo.web.smtp.port=25
edu.mayo.web.smtp.from.address=<EMAIL> 
edu.mayo.web.smtp.error.to.address=<EMAIL>

## log4j properties
log4j.rootLogger=info, console, logfile
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%5p - %m%n
log4j.appender.logfile=org.apache.log4j.RollingFileAppender
#log4j.appender.logfile.File=D:/linkchecker/log/linkchecker.log
log4j.appender.logfile.File=D:/linkchecker/log/linkchecker.log
log4j.appender.logfile.MaxFileSize=10MB
log4j.appender.logfile.MaxBackupIndex=10
log4j.appender.logfile.layout=org.apache.log4j.PatternLayout
log4j.appender.logfile.layout.ConversionPattern=%p [%t] (%F:%L) %t %c - %m%n
