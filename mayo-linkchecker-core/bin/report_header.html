<head>
<style>
a, a:visited, a:link {
	color: #0074a2;
	text-decoration: none;
	font-weight: 600;
}
a:hover {
	color: #2ea2cc;
}
a:active {
	color: #124964;	
}
.page-summary {
	font-style: italic;
	font-weight: 100;
}
body {
    text-decoration: none;
	color: rgb(102,102,102);
    line-height: 1.7em;
    font-size: 13px;
    font-family: "Open Sans",sans-serif;
	background-color: rgb(241,241,241);
    padding: 4px;
}

.summary-box, .page-box {
	position: relative;
    border-color: rgb(229,229,229);
    border-width: 1px;
    border-style: solid;
    background-color: rgb(255,255,255);
	margin-bottom: 10px;
	width: 100%;
	float:left;
}

.header {
	position: relative;
	padding: 4px;
    line-height: 1.4em;
	font-size: 14px;
	float:left;
	width: 100%;
}

.site-header {
	position: relative;
	padding: 4px;
    line-height: 1.4em;
	font-size: 23px;
	font-weight: 400;
	color: rgb(68,68,68);
}

.summary {
	margin: 2px;
    line-height: 1.4em;
    font-size: 13px;
    outline: 0px;
    padding: 2px;
	width: calc(100%-4px);
}

.inside {
	margin: 2px;
    line-height: 1.4em;
    font-size: 13px;
    outline: 0px;
    padding: 2px;
	padding-left:20px;
	width: calc(100%-20px);
}
.title {
	float:left;
	overflow: auto;
	font-weight: 600;
}
table {
    line-height: 1.1em;
    font-size: 13px;
	border-width:1px 0px 1px 0px;
	border-color: rgb(227,227,227);
	border-style:solid;
	width: 100%;
}
td, tr {
	border-width:0px;
    padding: 4px;
}
.arrow {
	float:left;
	padding: 7px;
}
.arrow-up {
	width: 0; 
	height: 0; 
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;	
	border-bottom: 5px solid rgb(68,68,68);
}
.arrow-down {
	width: 0; 
	height: 0; 
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 5px solid rgb(68,68,68);
}
.alternate {
	background-color: #f9f9f9;
}
.all-buttons {
	background-color: #f9f9f9;
}
</style>
</head>