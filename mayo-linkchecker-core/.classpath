<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry exported="true" kind="lib" path="lib/commons-cli-1.2.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/commons-configuration-1.10.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/json-org.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/jline-1.0.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/jsoup-1.5.2.jar" sourcepath="C:/Users/<USER>/AppData/Local/Temp/.org.sf.feeling.decompiler1631740024390/source/jsoup-1.5.2-sources.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/commons-io-1.4.jar"/>
	<classpathentry kind="lib" path="lib/log4j-1.2.16.jar"/>
	<classpathentry kind="lib" path="lib/mail.jar"/>
	<classpathentry kind="lib" path="lib/sqljdbc4.jar"/>
	<classpathentry kind="lib" path="lib/commons-codec-1.9.jar"/>
	<classpathentry kind="lib" path="lib/commons-logging-1.2.jar"/>
	<classpathentry kind="lib" path="lib/fluent-hc-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/httpclient-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/httpclient-cache-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/httpclient-win-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/httpcore-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/httpmime-4.4.1.jar"/>
	<classpathentry kind="lib" path="lib/jna-4.1.0.jar"/>
	<classpathentry kind="lib" path="lib/jna-platform-4.1.0.jar"/>
	<classpathentry kind="lib" path="lib/internal/servlet-api.jar"/>
	<classpathentry kind="lib" path="lib/jaxb-api-2.3.1.jar"/>
	<classpathentry kind="lib" path="lib/jaxb-impl-2.3.1.jar"/>
	<classpathentry kind="lib" path="lib/istack-commons-runtime-3.0.12.jar"/>
	<classpathentry kind="lib" path="lib/jakarta.xml.bind-api-2.3.3.jar"/>
	<classpathentry kind="lib" path="lib/mariadb-java-client-2.7.4.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="bin"/>
</classpath>
