<project name="mayo-linkchecker-core" default="all" basedir=".">
<property file="${basedir}/build.properties"/>	
<path id="class.path">
	<fileset file="${lib.dir}">
		<include name="**/*.jar"/>
	</fileset>
</path>
<target name="all">
	<antcall target="package.dsc"/>
</target>
<target name="compile" depends="prepare">
	<javac destdir="${classes.dir}" classpathref="class.path" debug="on" deprecation="on" optimize="off" target="1.7" source="1.7">
		<src path="${src.dir}"/>
	</javac>
</target>
<target name="package.dsc" description="mayo-linkchecker-core.jar" depends="compile">
	<jar destfile="${deploy.dir}/lib/mayo-linkchecker-core.jar">
		<zipfileset dir="${classes.dir}" includes="**/*.class"/>
		<zipfileset dir="${src.dir}" includes="**/*.xml,**/*.java,**/*.properties,**/*.cer,**/*.html"/>
		<fileset dir="." includes="icons/**"/>
		<fileset dir="." includes="lib/*.jar" excludes="lib/internal/*.jar"/>
		<fileset dir="." includes="sql/**"/>
	</jar>
	<copy file="./scripts/linkchecker.bat" todir="./deploy" />	
	<copy todir="./deploy/lib">
		<fileset dir="${lib.dir}"/>
	</copy>	
	<copy todir="./deploy/certificates">
		<fileset dir="${certs.dir}"/>
	</copy>	
</target>
<target name="prepare" depends="clean">
	<mkdir dir="${classes.dir}"/>
	<mkdir dir="${deploy.dir}"/>
	<mkdir dir="${lib.dir}"/>
</target>
<target name="clean">
	<delete dir="${classes.dir}" quiet="true"/>
	<delete dir="${deploy.dir}" quiet="true"/>
</target>
</project>